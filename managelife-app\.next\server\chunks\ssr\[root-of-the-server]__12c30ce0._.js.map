{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/docs/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { \n  ArrowLeft, \n  Building2, \n  Book,\n  FileText,\n  Code,\n  Zap,\n  Shield,\n  Users,\n  Coins,\n  ExternalLink\n} from 'lucide-react';\n\nexport default function DocsPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Home\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n              </div>\n            </div>\n            <Link\n              href=\"/auth/register\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n            >\n              Get Started\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <Book className=\"w-16 h-16 mx-auto mb-6\" />\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            Documentation\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Everything you need to know about using ManageLife platform, \n            from getting started to advanced features.\n          </p>\n        </div>\n      </section>\n\n      {/* Quick Start */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 text-center\">Quick Start Guide</h2>\n            \n            <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-blue-600 font-bold text-xl\">1</span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Create Account</h3>\n                <p className=\"text-gray-600\">Sign up with email or connect your MetaMask wallet to get started.</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-blue-600 font-bold text-xl\">2</span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Choose Your Role</h3>\n                <p className=\"text-gray-600\">Select your role: Homeowner, Renter, Buyer, Portfolio Manager, or Community Member.</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-blue-600 font-bold text-xl\">3</span>\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Start Exploring</h3>\n                <p className=\"text-gray-600\">Browse properties, join community events, and start earning $MLIFE tokens.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Documentation Sections */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Documentation Sections</h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Comprehensive guides for all aspects of the ManageLife platform\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* User Guide */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                <Users className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">User Guide</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Complete guide for using the platform, managing your profile, and navigating different features.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Account setup and verification</li>\n                <li>• Dashboard navigation</li>\n                <li>• Profile management</li>\n                <li>• Role switching</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n\n            {/* Property Management */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                <Building2 className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Property Management</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Learn how to list, manage, and tokenize properties on the ManageLife platform.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Property listing process</li>\n                <li>• NFT tokenization</li>\n                <li>• Rental management</li>\n                <li>• Maintenance tracking</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n\n            {/* Blockchain Integration */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                <Zap className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Blockchain Integration</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Understanding how blockchain technology powers the ManageLife ecosystem.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Wallet connection</li>\n                <li>• NFT creation and trading</li>\n                <li>• Smart contracts</li>\n                <li>• Transaction history</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n\n            {/* Token Economy */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4\">\n                <Coins className=\"w-6 h-6 text-yellow-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">$MLIFE Token Economy</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Learn about the $MLIFE token, how to earn rewards, and the platform economy.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Token earning mechanisms</li>\n                <li>• Reward system</li>\n                <li>• Token utility</li>\n                <li>• Staking and governance</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n\n            {/* API Documentation */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4\">\n                <Code className=\"w-6 h-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">API Documentation</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Technical documentation for developers integrating with ManageLife APIs.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Authentication</li>\n                <li>• Property APIs</li>\n                <li>• User management</li>\n                <li>• Webhook integration</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n\n            {/* Security */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4\">\n                <Shield className=\"w-6 h-6 text-indigo-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Security & Privacy</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Information about platform security, privacy policies, and best practices.\n              </p>\n              <ul className=\"space-y-2 text-sm text-gray-600 mb-4\">\n                <li>• Security measures</li>\n                <li>• Privacy policy</li>\n                <li>• Data protection</li>\n                <li>• Best practices</li>\n              </ul>\n              <Link href=\"#\" className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\">\n                Read Guide <ExternalLink className=\"w-4 h-4 ml-1\" />\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 text-center\">Frequently Asked Questions</h2>\n            \n            <div className=\"space-y-6\">\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">What is property tokenization?</h3>\n                <p className=\"text-gray-600\">\n                  Property tokenization is the process of converting real estate assets into digital tokens (NFTs) on the blockchain. \n                  This allows for fractional ownership, easier transfers, and transparent record-keeping.\n                </p>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">How do I earn $MLIFE tokens?</h3>\n                <p className=\"text-gray-600\">\n                  You can earn $MLIFE tokens through various activities: timely rent payments, property transactions, \n                  community participation, referrals, and completing platform milestones.\n                </p>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Is my data secure on the platform?</h3>\n                <p className=\"text-gray-600\">\n                  Yes, we use industry-standard encryption and security measures. Personal data is protected, \n                  while transaction records are stored securely on the blockchain for transparency.\n                </p>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Do I need a crypto wallet to use ManageLife?</h3>\n                <p className=\"text-gray-600\">\n                  While you can create an account with just an email, connecting a crypto wallet (like MetaMask) \n                  unlocks full blockchain features including NFT trading and token management.\n                </p>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">What blockchain networks are supported?</h3>\n                <p className=\"text-gray-600\">\n                  ManageLife currently supports Ethereum mainnet, Polygon, and Sepolia testnet. \n                  We're continuously evaluating additional networks based on user needs.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Support Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Need More Help?</h2>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Can't find what you're looking for? Our support team is here to help.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/support\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n            >\n              Contact Support\n            </Link>\n            <Link\n              href=\"/community\"\n              className=\"border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors\"\n            >\n              Join Community\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAae,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;0CAGtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;;;;;;0BAQhE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAElE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;0DAEpD,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;0DAEpD,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;0DAEpD,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAAkE;8DAC9E,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAElE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAM/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAM/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAM/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAM/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}