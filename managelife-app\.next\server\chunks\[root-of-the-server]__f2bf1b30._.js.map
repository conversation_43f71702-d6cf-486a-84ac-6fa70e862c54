{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { User, UserRole } from '@/types';\n\nconst DB_PATH = path.join(process.cwd(), 'data');\nconst USERS_FILE = path.join(DB_PATH, 'users.json');\nconst JWT_SECRET = process.env.JWT_SECRET || 'managelife-secret-key-2025';\n\n// Ensure data directory exists\nif (!fs.existsSync(DB_PATH)) {\n  fs.mkdirSync(DB_PATH, { recursive: true });\n}\n\n// Initialize users file if it doesn't exist\nif (!fs.existsSync(USERS_FILE)) {\n  fs.writeFileSync(USERS_FILE, JSON.stringify([], null, 2));\n}\n\ninterface UserData {\n  id: string;\n  email?: string;\n  name?: string;\n  password?: string;\n  walletAddress?: string;\n  roles: UserRole[];\n  kycStatus: 'pending' | 'verified' | 'rejected';\n  avatar?: string;\n  joinedAt: string;\n  mlifeBalance: number;\n  isEmailVerified: boolean;\n  lastLogin?: string;\n}\n\nclass Database {\n  private readUsers(): UserData[] {\n    try {\n      const data = fs.readFileSync(USERS_FILE, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      console.error('Error reading users file:', error);\n      return [];\n    }\n  }\n\n  private writeUsers(users: UserData[]): void {\n    try {\n      fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n    } catch (error) {\n      console.error('Error writing users file:', error);\n      throw new Error('Failed to save user data');\n    }\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  private generateToken(userId: string): string {\n    return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });\n  }\n\n  async createUser(userData: {\n    email?: string;\n    name?: string;\n    password?: string;\n    walletAddress?: string;\n    roles: UserRole[];\n  }): Promise<{ user: UserData; token: string }> {\n    const users = this.readUsers();\n    \n    // Check if user already exists\n    const existingUser = users.find(u => \n      (userData.email && u.email === userData.email) ||\n      (userData.walletAddress && u.walletAddress === userData.walletAddress)\n    );\n    \n    if (existingUser) {\n      throw new Error('User already exists');\n    }\n\n    // Hash password if provided\n    let hashedPassword: string | undefined;\n    if (userData.password) {\n      hashedPassword = await bcrypt.hash(userData.password, 12);\n    }\n\n    const newUser: UserData = {\n      id: this.generateId(),\n      email: userData.email,\n      name: userData.name,\n      password: hashedPassword,\n      walletAddress: userData.walletAddress,\n      roles: userData.roles,\n      kycStatus: 'pending',\n      joinedAt: new Date().toISOString(),\n      mlifeBalance: 1000, // Welcome bonus\n      isEmailVerified: false,\n      lastLogin: new Date().toISOString(),\n    };\n\n    users.push(newUser);\n    this.writeUsers(users);\n\n    const token = this.generateToken(newUser.id);\n    \n    // Remove password from response\n    const { password, ...userResponse } = newUser;\n    return { user: userResponse, token };\n  }\n\n  async authenticateUser(email: string, password: string): Promise<{ user: UserData; token: string }> {\n    const users = this.readUsers();\n    const user = users.find(u => u.email === email);\n    \n    if (!user || !user.password) {\n      throw new Error('Invalid credentials');\n    }\n\n    const isValidPassword = await bcrypt.compare(password, user.password);\n    if (!isValidPassword) {\n      throw new Error('Invalid credentials');\n    }\n\n    // Update last login\n    user.lastLogin = new Date().toISOString();\n    this.writeUsers(users);\n\n    const token = this.generateToken(user.id);\n    \n    // Remove password from response\n    const { password: _, ...userResponse } = user;\n    return { user: userResponse, token };\n  }\n\n  async authenticateWallet(walletAddress: string): Promise<{ user: UserData; token: string; isNewUser: boolean }> {\n    const users = this.readUsers();\n    let user = users.find(u => u.walletAddress === walletAddress.toLowerCase());\n    let isNewUser = false;\n\n    if (!user) {\n      // Create new user with wallet\n      const newUserData = await this.createUser({\n        walletAddress: walletAddress.toLowerCase(),\n        name: `User ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`,\n        roles: ['buyer'], // Default role for wallet users\n      });\n      user = newUserData.user;\n      isNewUser = true;\n    } else {\n      // Update last login\n      user.lastLogin = new Date().toISOString();\n      this.writeUsers(users);\n    }\n\n    const token = this.generateToken(user.id);\n    \n    // Remove password from response\n    const { password, ...userResponse } = user;\n    return { user: userResponse, token, isNewUser };\n  }\n\n  async getUserById(userId: string): Promise<UserData | null> {\n    const users = this.readUsers();\n    const user = users.find(u => u.id === userId);\n    \n    if (!user) {\n      return null;\n    }\n\n    // Remove password from response\n    const { password, ...userResponse } = user;\n    return userResponse;\n  }\n\n  async updateUser(userId: string, updates: Partial<UserData>): Promise<UserData> {\n    const users = this.readUsers();\n    const userIndex = users.findIndex(u => u.id === userId);\n\n    if (userIndex === -1) {\n      throw new Error('User not found');\n    }\n\n    // Don't allow updating sensitive fields directly\n    const { id, password, ...allowedUpdates } = updates;\n\n    users[userIndex] = { ...users[userIndex], ...allowedUpdates };\n    this.writeUsers(users);\n\n    // Remove password from response\n    const { password: _, ...userResponse } = users[userIndex];\n    return userResponse;\n  }\n\n  // Update user's MLIFE balance\n  async updateUserBalance(userId: string, amount: number): Promise<void> {\n    const users = this.readUsers();\n    const userIndex = users.findIndex(u => u.id === userId);\n\n    if (userIndex === -1) {\n      throw new Error('User not found');\n    }\n\n    users[userIndex].mlifeBalance += amount;\n    this.writeUsers(users);\n  }\n\n  async linkWalletToUser(userId: string, walletAddress: string): Promise<UserData> {\n    const users = this.readUsers();\n    const userIndex = users.findIndex(u => u.id === userId);\n    \n    if (userIndex === -1) {\n      throw new Error('User not found');\n    }\n\n    // Check if wallet is already linked to another user\n    const existingWalletUser = users.find(u => u.walletAddress === walletAddress.toLowerCase() && u.id !== userId);\n    if (existingWalletUser) {\n      throw new Error('Wallet already linked to another account');\n    }\n\n    users[userIndex].walletAddress = walletAddress.toLowerCase();\n    this.writeUsers(users);\n\n    // Remove password from response\n    const { password, ...userResponse } = users[userIndex];\n    return userResponse;\n  }\n\n  verifyToken(token: string): { userId: string } | null {\n    try {\n      const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };\n      return decoded;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  async getAllUsers(): Promise<UserData[]> {\n    const users = this.readUsers();\n    // Remove passwords from response\n    return users.map(({ password, ...user }) => user);\n  }\n\n  async updateUserBalance(userId: string, amount: number): Promise<UserData> {\n    const users = this.readUsers();\n    const userIndex = users.findIndex(u => u.id === userId);\n    \n    if (userIndex === -1) {\n      throw new Error('User not found');\n    }\n\n    users[userIndex].mlifeBalance += amount;\n    this.writeUsers(users);\n\n    // Remove password from response\n    const { password, ...userResponse } = users[userIndex];\n    return userResponse;\n  }\n}\n\nexport const db = new Database();\nexport default db;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAGA,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AACzC,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACtC,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAE7C,+BAA+B;AAC/B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,4CAA4C;AAC5C,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;IAC9B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;AACxD;AAiBA,MAAM;IACI,YAAwB;QAC9B,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,EAAE;QACX;IACF;IAEQ,WAAW,KAAiB,EAAQ;QAC1C,IAAI;YACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,aAAqB;QAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEQ,cAAc,MAAc,EAAU;QAC5C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC;YAAE;QAAO,GAAG,YAAY;YAAE,WAAW;QAAK;IAC5D;IAEA,MAAM,WAAW,QAMhB,EAA8C;QAC7C,MAAM,QAAQ,IAAI,CAAC,SAAS;QAE5B,+BAA+B;QAC/B,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAC9B,AAAC,SAAS,KAAK,IAAI,EAAE,KAAK,KAAK,SAAS,KAAK,IAC5C,SAAS,aAAa,IAAI,EAAE,aAAa,KAAK,SAAS,aAAa;QAGvE,IAAI,cAAc;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,4BAA4B;QAC5B,IAAI;QACJ,IAAI,SAAS,QAAQ,EAAE;YACrB,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;QACxD;QAEA,MAAM,UAAoB;YACxB,IAAI,IAAI,CAAC,UAAU;YACnB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;YACnB,UAAU;YACV,eAAe,SAAS,aAAa;YACrC,OAAO,SAAS,KAAK;YACrB,WAAW;YACX,UAAU,IAAI,OAAO,WAAW;YAChC,cAAc;YACd,iBAAiB;YACjB,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,UAAU,CAAC;QAEhB,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;QAE3C,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG;QACtC,OAAO;YAAE,MAAM;YAAc;QAAM;IACrC;IAEA,MAAM,iBAAiB,KAAa,EAAE,QAAgB,EAA8C;QAClG,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAEzC,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;QACpE,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,oBAAoB;QACpB,KAAK,SAAS,GAAG,IAAI,OAAO,WAAW;QACvC,IAAI,CAAC,UAAU,CAAC;QAEhB,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;QAExC,gCAAgC;QAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,cAAc,GAAG;QACzC,OAAO;YAAE,MAAM;YAAc;QAAM;IACrC;IAEA,MAAM,mBAAmB,aAAqB,EAAkE;QAC9G,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,IAAI,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,cAAc,WAAW;QACxE,IAAI,YAAY;QAEhB,IAAI,CAAC,MAAM;YACT,8BAA8B;YAC9B,MAAM,cAAc,MAAM,IAAI,CAAC,UAAU,CAAC;gBACxC,eAAe,cAAc,WAAW;gBACxC,MAAM,CAAC,KAAK,EAAE,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,cAAc,KAAK,CAAC,CAAC,IAAI;gBACtE,OAAO;oBAAC;iBAAQ;YAClB;YACA,OAAO,YAAY,IAAI;YACvB,YAAY;QACd,OAAO;YACL,oBAAoB;YACpB,KAAK,SAAS,GAAG,IAAI,OAAO,WAAW;YACvC,IAAI,CAAC,UAAU,CAAC;QAClB;QAEA,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;QAExC,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG;QACtC,OAAO;YAAE,MAAM;YAAc;YAAO;QAAU;IAChD;IAEA,MAAM,YAAY,MAAc,EAA4B;QAC1D,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG;QACtC,OAAO;IACT;IAEA,MAAM,WAAW,MAAc,EAAE,OAA0B,EAAqB;QAC9E,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEhD,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,iDAAiD;QACjD,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,gBAAgB,GAAG;QAE5C,KAAK,CAAC,UAAU,GAAG;YAAE,GAAG,KAAK,CAAC,UAAU;YAAE,GAAG,cAAc;QAAC;QAC5D,IAAI,CAAC,UAAU,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,MAAc,EAAE,MAAc,EAAiB;QACrE,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEhD,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,KAAK,CAAC,UAAU,CAAC,YAAY,IAAI;QACjC,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA,MAAM,iBAAiB,MAAc,EAAE,aAAqB,EAAqB;QAC/E,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEhD,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,MAAM,qBAAqB,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,cAAc,WAAW,MAAM,EAAE,EAAE,KAAK;QACvG,IAAI,oBAAoB;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,KAAK,CAAC,UAAU,CAAC,aAAa,GAAG,cAAc,WAAW;QAC1D,IAAI,CAAC,UAAU,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,UAAU;QACtD,OAAO;IACT;IAEA,YAAY,KAAa,EAA6B;QACpD,IAAI;YACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,cAAmC;QACvC,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,iCAAiC;QACjC,OAAO,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,MAAM,GAAK;IAC9C;IAEA,MAAM,kBAAkB,MAAc,EAAE,MAAc,EAAqB;QACzE,MAAM,QAAQ,IAAI,CAAC,SAAS;QAC5B,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEhD,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,KAAK,CAAC,UAAU,CAAC,YAAY,IAAI;QACjC,IAAI,CAAC,UAAU,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,GAAG,KAAK,CAAC,UAAU;QACtD,OAAO;IACT;AACF;AAEO,MAAM,KAAK,IAAI;uCACP", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/lib/community.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { \n  CommunityEvent, \n  EventParticipant, \n  CommunityPost, \n  PostComment, \n  PostLike, \n  UserProfile,\n  CommunityBadge,\n  EventType,\n  PostType,\n  PostCategory\n} from '@/types';\n\nconst DB_PATH = path.join(process.cwd(), 'data');\nconst EVENTS_FILE = path.join(DB_PATH, 'community-events.json');\nconst PARTICIPANTS_FILE = path.join(DB_PATH, 'event-participants.json');\nconst POSTS_FILE = path.join(DB_PATH, 'community-posts.json');\nconst COMMENTS_FILE = path.join(DB_PATH, 'post-comments.json');\nconst LIKES_FILE = path.join(DB_PATH, 'post-likes.json');\nconst PROFILES_FILE = path.join(DB_PATH, 'user-profiles.json');\n\n// Ensure data directory exists\nif (!fs.existsSync(DB_PATH)) {\n  fs.mkdirSync(DB_PATH, { recursive: true });\n}\n\n// Initialize files if they don't exist\nconst initializeFile = (filePath: string, defaultData: any[]) => {\n  if (!fs.existsSync(filePath)) {\n    fs.writeFileSync(filePath, JSON.stringify(defaultData, null, 2));\n  }\n};\n\n// Initialize with sample data\nconst sampleEvents: CommunityEvent[] = [\n  {\n    id: 'event-1',\n    title: 'Real Estate Investment Fundamentals',\n    description: 'Learn the basics of real estate investment, including market analysis, financing options, and risk management strategies.',\n    type: 'webinar',\n    startDate: new Date('2025-02-15T19:00:00Z'),\n    endDate: new Date('2025-02-15T20:30:00Z'),\n    isVirtual: true,\n    maxParticipants: 100,\n    currentParticipants: 45,\n    organizerId: 'admin-1',\n    status: 'upcoming',\n    tags: ['investment', 'beginner', 'fundamentals'],\n    rewardAmount: 25,\n    createdAt: new Date('2025-01-15T10:00:00Z'),\n    updatedAt: new Date('2025-01-15T10:00:00Z'),\n  },\n  {\n    id: 'event-2',\n    title: 'Property Management Best Practices',\n    description: 'Workshop covering tenant screening, maintenance scheduling, and legal compliance for property managers.',\n    type: 'workshop',\n    startDate: new Date('2025-02-20T14:00:00Z'),\n    endDate: new Date('2025-02-20T17:00:00Z'),\n    location: 'Downtown Conference Center',\n    isVirtual: false,\n    maxParticipants: 30,\n    currentParticipants: 18,\n    organizerId: 'admin-1',\n    status: 'upcoming',\n    tags: ['property-management', 'workshop', 'legal'],\n    rewardAmount: 50,\n    createdAt: new Date('2025-01-20T10:00:00Z'),\n    updatedAt: new Date('2025-01-20T10:00:00Z'),\n  },\n];\n\nconst samplePosts: CommunityPost[] = [\n  {\n    id: 'post-1',\n    authorId: 'user-1',\n    title: 'Market Trends: What to Expect in 2025',\n    content: 'Based on recent data and expert analysis, here are the key real estate market trends we should watch for in 2025...',\n    type: 'discussion',\n    category: 'market_trends',\n    tags: ['2025', 'trends', 'analysis'],\n    likes: 24,\n    comments: 8,\n    shares: 5,\n    isSticky: true,\n    status: 'published',\n    createdAt: new Date('2025-01-10T09:00:00Z'),\n    updatedAt: new Date('2025-01-10T09:00:00Z'),\n  },\n  {\n    id: 'post-2',\n    authorId: 'user-2',\n    title: 'First-time Buyer Tips',\n    content: 'As someone who just bought their first property, here are some tips I wish I knew earlier...',\n    type: 'tip',\n    category: 'general',\n    tags: ['first-time', 'tips', 'buying'],\n    likes: 18,\n    comments: 12,\n    shares: 7,\n    isSticky: false,\n    status: 'published',\n    createdAt: new Date('2025-01-12T14:30:00Z'),\n    updatedAt: new Date('2025-01-12T14:30:00Z'),\n  },\n];\n\ninitializeFile(EVENTS_FILE, sampleEvents);\ninitializeFile(PARTICIPANTS_FILE, []);\ninitializeFile(POSTS_FILE, samplePosts);\ninitializeFile(COMMENTS_FILE, []);\ninitializeFile(LIKES_FILE, []);\ninitializeFile(PROFILES_FILE, []);\n\nclass CommunityService {\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  // Event management\n  async getEvents(filters?: {\n    type?: EventType;\n    status?: string;\n    upcoming?: boolean;\n  }): Promise<CommunityEvent[]> {\n    try {\n      const data = fs.readFileSync(EVENTS_FILE, 'utf8');\n      let events: CommunityEvent[] = JSON.parse(data);\n\n      // Convert date strings back to Date objects\n      events = events.map(event => ({\n        ...event,\n        startDate: new Date(event.startDate),\n        endDate: new Date(event.endDate),\n        createdAt: new Date(event.createdAt),\n        updatedAt: new Date(event.updatedAt),\n      }));\n\n      // Apply filters\n      if (filters) {\n        if (filters.type) {\n          events = events.filter(event => event.type === filters.type);\n        }\n        if (filters.status) {\n          events = events.filter(event => event.status === filters.status);\n        }\n        if (filters.upcoming) {\n          events = events.filter(event => new Date(event.startDate) > new Date());\n        }\n      }\n\n      // Sort by start date\n      return events.sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());\n    } catch (error) {\n      console.error('Error reading events:', error);\n      return [];\n    }\n  }\n\n  async getEventById(eventId: string): Promise<CommunityEvent | null> {\n    const events = await this.getEvents();\n    return events.find(event => event.id === eventId) || null;\n  }\n\n  async createEvent(eventData: Omit<CommunityEvent, 'id' | 'currentParticipants' | 'createdAt' | 'updatedAt'>): Promise<CommunityEvent> {\n    const events = await this.getEvents();\n    \n    const newEvent: CommunityEvent = {\n      ...eventData,\n      id: this.generateId(),\n      currentParticipants: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    events.push(newEvent);\n    fs.writeFileSync(EVENTS_FILE, JSON.stringify(events, null, 2));\n    \n    return newEvent;\n  }\n\n  // Event participation\n  async registerForEvent(eventId: string, userId: string): Promise<EventParticipant> {\n    const participants = this.readParticipants();\n    \n    // Check if already registered\n    const existing = participants.find(p => p.eventId === eventId && p.userId === userId);\n    if (existing) {\n      throw new Error('Already registered for this event');\n    }\n\n    const newParticipant: EventParticipant = {\n      id: this.generateId(),\n      eventId,\n      userId,\n      status: 'registered',\n      registeredAt: new Date(),\n    };\n\n    participants.push(newParticipant);\n    fs.writeFileSync(PARTICIPANTS_FILE, JSON.stringify(participants, null, 2));\n\n    // Update event participant count\n    await this.updateEventParticipantCount(eventId);\n\n    return newParticipant;\n  }\n\n  private readParticipants(): EventParticipant[] {\n    try {\n      const data = fs.readFileSync(PARTICIPANTS_FILE, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      return [];\n    }\n  }\n\n  private async updateEventParticipantCount(eventId: string): Promise<void> {\n    const events = await this.getEvents();\n    const participants = this.readParticipants();\n    \n    const eventIndex = events.findIndex(e => e.id === eventId);\n    if (eventIndex !== -1) {\n      const participantCount = participants.filter(p => \n        p.eventId === eventId && p.status !== 'cancelled'\n      ).length;\n      \n      events[eventIndex].currentParticipants = participantCount;\n      events[eventIndex].updatedAt = new Date();\n      \n      fs.writeFileSync(EVENTS_FILE, JSON.stringify(events, null, 2));\n    }\n  }\n\n  // Posts management\n  async getPosts(filters?: {\n    category?: PostCategory;\n    type?: PostType;\n    authorId?: string;\n  }): Promise<CommunityPost[]> {\n    try {\n      const data = fs.readFileSync(POSTS_FILE, 'utf8');\n      let posts: CommunityPost[] = JSON.parse(data);\n\n      // Convert date strings back to Date objects\n      posts = posts.map(post => ({\n        ...post,\n        createdAt: new Date(post.createdAt),\n        updatedAt: new Date(post.updatedAt),\n      }));\n\n      // Apply filters\n      if (filters) {\n        if (filters.category) {\n          posts = posts.filter(post => post.category === filters.category);\n        }\n        if (filters.type) {\n          posts = posts.filter(post => post.type === filters.type);\n        }\n        if (filters.authorId) {\n          posts = posts.filter(post => post.authorId === filters.authorId);\n        }\n      }\n\n      // Sort by creation date (newest first), with sticky posts at top\n      return posts.sort((a, b) => {\n        if (a.isSticky && !b.isSticky) return -1;\n        if (!a.isSticky && b.isSticky) return 1;\n        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n      });\n    } catch (error) {\n      console.error('Error reading posts:', error);\n      return [];\n    }\n  }\n\n  async createPost(postData: Omit<CommunityPost, 'id' | 'likes' | 'comments' | 'shares' | 'createdAt' | 'updatedAt'>): Promise<CommunityPost> {\n    const posts = await this.getPosts();\n    \n    const newPost: CommunityPost = {\n      ...postData,\n      id: this.generateId(),\n      likes: 0,\n      comments: 0,\n      shares: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    posts.push(newPost);\n    fs.writeFileSync(POSTS_FILE, JSON.stringify(posts, null, 2));\n    \n    return newPost;\n  }\n\n  // Community stats\n  async getCommunityStats(): Promise<any> {\n    const events = await this.getEvents();\n    const posts = await this.getPosts();\n    const participants = this.readParticipants();\n\n    return {\n      totalEvents: events.length,\n      upcomingEvents: events.filter(e => e.status === 'upcoming').length,\n      totalPosts: posts.length,\n      totalParticipants: participants.length,\n      activeMembers: new Set(participants.map(p => p.userId)).size,\n    };\n  }\n}\n\nexport const communityService = new CommunityService();\nexport default communityService;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAcA,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AACzC,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACvC,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AAC7C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACtC,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACzC,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACtC,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AAEzC,+BAA+B;AAC/B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,uCAAuC;AACvC,MAAM,iBAAiB,CAAC,UAAkB;IACxC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,aAAa,MAAM;IAC/D;AACF;AAEA,8BAA8B;AAC9B,MAAM,eAAiC;IACrC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,WAAW;QACX,iBAAiB;QACjB,qBAAqB;QACrB,aAAa;QACb,QAAQ;QACR,MAAM;YAAC;YAAc;YAAY;SAAe;QAChD,cAAc;QACd,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,UAAU;QACV,WAAW;QACX,iBAAiB;QACjB,qBAAqB;QACrB,aAAa;QACb,QAAQ;QACR,MAAM;YAAC;YAAuB;YAAY;SAAQ;QAClD,cAAc;QACd,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,MAAM,cAA+B;IACnC;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;YAAU;SAAW;QACpC,OAAO;QACP,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAc;YAAQ;SAAS;QACtC,OAAO;QACP,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,eAAe,aAAa;AAC5B,eAAe,mBAAmB,EAAE;AACpC,eAAe,YAAY;AAC3B,eAAe,eAAe,EAAE;AAChC,eAAe,YAAY,EAAE;AAC7B,eAAe,eAAe,EAAE;AAEhC,MAAM;IACI,aAAqB;QAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA,mBAAmB;IACnB,MAAM,UAAU,OAIf,EAA6B;QAC5B,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,aAAa;YAC1C,IAAI,SAA2B,KAAK,KAAK,CAAC;YAE1C,4CAA4C;YAC5C,SAAS,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC5B,GAAG,KAAK;oBACR,WAAW,IAAI,KAAK,MAAM,SAAS;oBACnC,SAAS,IAAI,KAAK,MAAM,OAAO;oBAC/B,WAAW,IAAI,KAAK,MAAM,SAAS;oBACnC,WAAW,IAAI,KAAK,MAAM,SAAS;gBACrC,CAAC;YAED,gBAAgB;YAChB,IAAI,SAAS;gBACX,IAAI,QAAQ,IAAI,EAAE;oBAChB,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,QAAQ,IAAI;gBAC7D;gBACA,IAAI,QAAQ,MAAM,EAAE;oBAClB,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,QAAQ,MAAM;gBACjE;gBACA,IAAI,QAAQ,QAAQ,EAAE;oBACpB,SAAS,OAAO,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,SAAS,IAAI,IAAI;gBAClE;YACF;YAEA,qBAAqB;YACrB,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;IACF;IAEA,MAAM,aAAa,OAAe,EAAkC;QAClE,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,YAAY;IACvD;IAEA,MAAM,YAAY,SAAyF,EAA2B;QACpI,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QAEnC,MAAM,WAA2B;YAC/B,GAAG,SAAS;YACZ,IAAI,IAAI,CAAC,UAAU;YACnB,qBAAqB;YACrB,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QAEA,OAAO,IAAI,CAAC;QACZ,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;QAE3D,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,iBAAiB,OAAe,EAAE,MAAc,EAA6B;QACjF,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAE1C,8BAA8B;QAC9B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,WAAW,EAAE,MAAM,KAAK;QAC9E,IAAI,UAAU;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAmC;YACvC,IAAI,IAAI,CAAC,UAAU;YACnB;YACA;YACA,QAAQ;YACR,cAAc,IAAI;QACpB;QAEA,aAAa,IAAI,CAAC;QAClB,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,CAAC,cAAc,MAAM;QAEvE,iCAAiC;QACjC,MAAM,IAAI,CAAC,2BAA2B,CAAC;QAEvC,OAAO;IACT;IAEQ,mBAAuC;QAC7C,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,mBAAmB;YAChD,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,OAAO,EAAE;QACX;IACF;IAEA,MAAc,4BAA4B,OAAe,EAAiB;QACxE,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAE1C,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAClD,IAAI,eAAe,CAAC,GAAG;YACrB,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA,IAC3C,EAAE,OAAO,KAAK,WAAW,EAAE,MAAM,KAAK,aACtC,MAAM;YAER,MAAM,CAAC,WAAW,CAAC,mBAAmB,GAAG;YACzC,MAAM,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI;YAEnC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;QAC7D;IACF;IAEA,mBAAmB;IACnB,MAAM,SAAS,OAId,EAA4B;QAC3B,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;YACzC,IAAI,QAAyB,KAAK,KAAK,CAAC;YAExC,4CAA4C;YAC5C,QAAQ,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,WAAW,IAAI,KAAK,KAAK,SAAS;oBAClC,WAAW,IAAI,KAAK,KAAK,SAAS;gBACpC,CAAC;YAED,gBAAgB;YAChB,IAAI,SAAS;gBACX,IAAI,QAAQ,QAAQ,EAAE;oBACpB,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,QAAQ,QAAQ;gBACjE;gBACA,IAAI,QAAQ,IAAI,EAAE;oBAChB,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,IAAI;gBACzD;gBACA,IAAI,QAAQ,QAAQ,EAAE;oBACpB,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,QAAQ,QAAQ;gBACjE;YACF;YAEA,iEAAiE;YACjE,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;gBACpB,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;gBACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;gBACtC,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,EAAE;QACX;IACF;IAEA,MAAM,WAAW,QAAiG,EAA0B;QAC1I,MAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ;QAEjC,MAAM,UAAyB;YAC7B,GAAG,QAAQ;YACX,IAAI,IAAI,CAAC,UAAU;YACnB,OAAO;YACP,UAAU;YACV,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QAEA,MAAM,IAAI,CAAC;QACX,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;QAEzD,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,oBAAkC;QACtC,MAAM,SAAS,MAAM,IAAI,CAAC,SAAS;QACnC,MAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACjC,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAE1C,OAAO;YACL,aAAa,OAAO,MAAM;YAC1B,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YAClE,YAAY,MAAM,MAAM;YACxB,mBAAmB,aAAa,MAAM;YACtC,eAAe,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG,IAAI;QAC9D;IACF;AACF;AAEO,MAAM,mBAAmB,IAAI;uCACrB", "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/lib/rewards.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { Reward, RewardType, RewardRule, UserRewardStats, Achievement } from '@/types';\n\nconst DB_PATH = path.join(process.cwd(), 'data');\nconst REWARDS_FILE = path.join(DB_PATH, 'rewards.json');\nconst REWARD_RULES_FILE = path.join(DB_PATH, 'reward-rules.json');\nconst USER_STATS_FILE = path.join(DB_PATH, 'user-reward-stats.json');\n\n// Ensure data directory exists\nif (!fs.existsSync(DB_PATH)) {\n  fs.mkdirSync(DB_PATH, { recursive: true });\n}\n\n// Initialize files if they don't exist\nif (!fs.existsSync(REWARDS_FILE)) {\n  fs.writeFileSync(REWARDS_FILE, JSON.stringify([], null, 2));\n}\n\nif (!fs.existsSync(REWARD_RULES_FILE)) {\n  // Initialize with default reward rules\n  const defaultRules: RewardRule[] = [\n    {\n      id: 'welcome_bonus',\n      type: 'welcome_bonus',\n      name: 'Welcome Bonus',\n      description: 'Welcome to ManageLife! Here\\'s your starter bonus.',\n      amount: 1000,\n      conditions: {},\n      isActive: true,\n      maxClaims: 1,\n    },\n    {\n      id: 'daily_login',\n      type: 'daily_login',\n      name: 'Daily Login Bonus',\n      description: 'Login daily to earn rewards!',\n      amount: 10,\n      conditions: {},\n      isActive: true,\n      cooldownPeriod: 24,\n    },\n    {\n      id: 'rent_payment',\n      type: 'rent_payment',\n      name: 'On-time Rent Payment',\n      description: 'Reward for paying rent on time',\n      amount: 50,\n      conditions: { onTime: true },\n      isActive: true,\n    },\n    {\n      id: 'property_listing',\n      type: 'property_listing',\n      name: 'Property Listing Bonus',\n      description: 'Reward for listing a property',\n      amount: 100,\n      conditions: {},\n      isActive: true,\n    },\n    {\n      id: 'referral',\n      type: 'referral',\n      name: 'Referral Bonus',\n      description: 'Reward for referring new users',\n      amount: 200,\n      conditions: {},\n      isActive: true,\n    },\n    {\n      id: 'kyc_completion',\n      type: 'kyc_completion',\n      name: 'KYC Completion Bonus',\n      description: 'Reward for completing KYC verification',\n      amount: 150,\n      conditions: {},\n      isActive: true,\n      maxClaims: 1,\n    },\n    {\n      id: 'community_participation',\n      type: 'community_participation',\n      name: 'Community Participation',\n      description: 'Reward for participating in community activities',\n      amount: 25,\n      conditions: {},\n      isActive: true,\n      cooldownPeriod: 1, // 1 hour cooldown between community rewards\n    },\n  ];\n  fs.writeFileSync(REWARD_RULES_FILE, JSON.stringify(defaultRules, null, 2));\n}\n\nif (!fs.existsSync(USER_STATS_FILE)) {\n  fs.writeFileSync(USER_STATS_FILE, JSON.stringify([], null, 2));\n}\n\nclass RewardSystem {\n  private readRewards(): Reward[] {\n    try {\n      const data = fs.readFileSync(REWARDS_FILE, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      console.error('Error reading rewards file:', error);\n      return [];\n    }\n  }\n\n  private writeRewards(rewards: Reward[]): void {\n    try {\n      fs.writeFileSync(REWARDS_FILE, JSON.stringify(rewards, null, 2));\n    } catch (error) {\n      console.error('Error writing rewards file:', error);\n      throw new Error('Failed to save reward data');\n    }\n  }\n\n  private readRewardRules(): RewardRule[] {\n    try {\n      const data = fs.readFileSync(REWARD_RULES_FILE, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      console.error('Error reading reward rules file:', error);\n      return [];\n    }\n  }\n\n  private readUserStats(): UserRewardStats[] {\n    try {\n      const data = fs.readFileSync(USER_STATS_FILE, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      console.error('Error reading user stats file:', error);\n      return [];\n    }\n  }\n\n  private writeUserStats(stats: UserRewardStats[]): void {\n    try {\n      fs.writeFileSync(USER_STATS_FILE, JSON.stringify(stats, null, 2));\n    } catch (error) {\n      console.error('Error writing user stats file:', error);\n      throw new Error('Failed to save user stats');\n    }\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  // Create a new reward\n  async createReward(\n    userId: string,\n    type: RewardType,\n    amount: number,\n    description: string,\n    metadata?: Record<string, any>\n  ): Promise<Reward> {\n    const rewards = this.readRewards();\n    \n    const newReward: Reward = {\n      id: this.generateId(),\n      userId,\n      amount,\n      source: type,\n      description,\n      status: 'pending',\n      createdAt: new Date(),\n      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days\n      metadata,\n    };\n\n    rewards.push(newReward);\n    this.writeRewards(rewards);\n\n    // Update user stats\n    await this.updateUserStats(userId, amount, 0);\n\n    return newReward;\n  }\n\n  // Get rewards for a user\n  async getUserRewards(userId: string): Promise<Reward[]> {\n    const rewards = this.readRewards();\n    return rewards.filter(reward => reward.userId === userId);\n  }\n\n  // Get pending rewards for a user\n  async getPendingRewards(userId: string): Promise<Reward[]> {\n    const rewards = await this.getUserRewards(userId);\n    return rewards.filter(reward => \n      reward.status === 'pending' && \n      (!reward.expiresAt || new Date(reward.expiresAt) > new Date())\n    );\n  }\n\n  // Claim a reward\n  async claimReward(rewardId: string, userId: string): Promise<Reward> {\n    const rewards = this.readRewards();\n    const rewardIndex = rewards.findIndex(r => r.id === rewardId && r.userId === userId);\n    \n    if (rewardIndex === -1) {\n      throw new Error('Reward not found');\n    }\n\n    const reward = rewards[rewardIndex];\n    \n    if (reward.status !== 'pending') {\n      throw new Error('Reward already claimed or expired');\n    }\n\n    if (reward.expiresAt && new Date(reward.expiresAt) <= new Date()) {\n      reward.status = 'expired';\n      this.writeRewards(rewards);\n      throw new Error('Reward has expired');\n    }\n\n    reward.status = 'claimed';\n    reward.claimedAt = new Date();\n    \n    this.writeRewards(rewards);\n\n    // Update user stats\n    await this.updateUserStats(userId, 0, reward.amount);\n\n    return reward;\n  }\n\n  // Claim all pending rewards for a user\n  async claimAllRewards(userId: string): Promise<{ claimed: Reward[]; total: number }> {\n    const pendingRewards = await this.getPendingRewards(userId);\n    const claimed: Reward[] = [];\n    let total = 0;\n\n    for (const reward of pendingRewards) {\n      try {\n        const claimedReward = await this.claimReward(reward.id, userId);\n        claimed.push(claimedReward);\n        total += claimedReward.amount;\n      } catch (error) {\n        console.error(`Failed to claim reward ${reward.id}:`, error);\n      }\n    }\n\n    return { claimed, total };\n  }\n\n  // Award reward based on activity\n  async awardReward(userId: string, type: RewardType, metadata?: Record<string, any>): Promise<Reward | null> {\n    const rules = this.readRewardRules();\n    const rule = rules.find(r => r.type === type && r.isActive);\n    \n    if (!rule) {\n      console.log(`No active rule found for reward type: ${type}`);\n      return null;\n    }\n\n    // Check cooldown period\n    if (rule.cooldownPeriod) {\n      const userRewards = await this.getUserRewards(userId);\n      const lastReward = userRewards\n        .filter(r => r.source === type)\n        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];\n      \n      if (lastReward) {\n        const timeSinceLastReward = Date.now() - new Date(lastReward.createdAt).getTime();\n        const cooldownMs = rule.cooldownPeriod * 60 * 60 * 1000;\n        \n        if (timeSinceLastReward < cooldownMs) {\n          console.log(`Cooldown period not met for reward type: ${type}`);\n          return null;\n        }\n      }\n    }\n\n    // Check max claims\n    if (rule.maxClaims) {\n      const userRewards = await this.getUserRewards(userId);\n      const claimsCount = userRewards.filter(r => r.source === type).length;\n      \n      if (claimsCount >= rule.maxClaims) {\n        console.log(`Max claims reached for reward type: ${type}`);\n        return null;\n      }\n    }\n\n    return await this.createReward(userId, type, rule.amount, rule.description, metadata);\n  }\n\n  // Get user reward statistics\n  async getUserStats(userId: string): Promise<UserRewardStats> {\n    const allStats = this.readUserStats();\n    let userStats = allStats.find(s => s.userId === userId);\n    \n    if (!userStats) {\n      userStats = {\n        userId,\n        totalEarned: 0,\n        totalClaimed: 0,\n        pendingRewards: 0,\n        streakDays: 0,\n        achievements: [],\n      };\n      allStats.push(userStats);\n      this.writeUserStats(allStats);\n    }\n\n    // Update pending rewards count\n    const pendingRewards = await this.getPendingRewards(userId);\n    userStats.pendingRewards = pendingRewards.reduce((sum, reward) => sum + reward.amount, 0);\n\n    return userStats;\n  }\n\n  // Update user statistics\n  private async updateUserStats(userId: string, earnedAmount: number, claimedAmount: number): Promise<void> {\n    const allStats = this.readUserStats();\n    let userStatsIndex = allStats.findIndex(s => s.userId === userId);\n    \n    if (userStatsIndex === -1) {\n      allStats.push({\n        userId,\n        totalEarned: earnedAmount,\n        totalClaimed: claimedAmount,\n        pendingRewards: 0,\n        streakDays: 0,\n        achievements: [],\n      });\n    } else {\n      allStats[userStatsIndex].totalEarned += earnedAmount;\n      allStats[userStatsIndex].totalClaimed += claimedAmount;\n      \n      if (claimedAmount > 0) {\n        allStats[userStatsIndex].lastClaimDate = new Date();\n      }\n    }\n\n    this.writeUserStats(allStats);\n  }\n\n  // Get reward rules\n  async getRewardRules(): Promise<RewardRule[]> {\n    return this.readRewardRules();\n  }\n\n  // Get leaderboard\n  async getLeaderboard(limit: number = 10): Promise<UserRewardStats[]> {\n    const allStats = this.readUserStats();\n    return allStats\n      .sort((a, b) => b.totalClaimed - a.totalClaimed)\n      .slice(0, limit);\n  }\n}\n\nexport const rewardSystem = new RewardSystem();\nexport default rewardSystem;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGA,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AACzC,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AACxC,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AAC7C,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;AAE3C,+BAA+B;AAC/B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,uCAAuC;AACvC,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,eAAe;IAChC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;AAC1D;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,oBAAoB;IACrC,uCAAuC;IACvC,MAAM,eAA6B;QACjC;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY;gBAAE,QAAQ;YAAK;YAC3B,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;YACV,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,QAAQ;YACR,YAAY,CAAC;YACb,UAAU;YACV,gBAAgB;QAClB;KACD;IACD,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,CAAC,cAAc,MAAM;AACzE;AAEA,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,kBAAkB;IACnC,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,iBAAiB,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;AAC7D;AAEA,MAAM;IACI,cAAwB;QAC9B,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,cAAc;YAC3C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;IAEQ,aAAa,OAAiB,EAAQ;QAC5C,IAAI;YACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,CAAC,SAAS,MAAM;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,kBAAgC;QACtC,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,mBAAmB;YAChD,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEQ,gBAAmC;QACzC,IAAI;YACF,MAAM,OAAO,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iBAAiB;YAC9C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;IACF;IAEQ,eAAe,KAAwB,EAAQ;QACrD,IAAI;YACF,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,iBAAiB,KAAK,SAAS,CAAC,OAAO,MAAM;QAChE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,aAAqB;QAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA,sBAAsB;IACtB,MAAM,aACJ,MAAc,EACd,IAAgB,EAChB,MAAc,EACd,WAAmB,EACnB,QAA8B,EACb;QACjB,MAAM,UAAU,IAAI,CAAC,WAAW;QAEhC,MAAM,YAAoB;YACxB,IAAI,IAAI,CAAC,UAAU;YACnB;YACA;YACA,QAAQ;YACR;YACA,QAAQ;YACR,WAAW,IAAI;YACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YACrD;QACF;QAEA,QAAQ,IAAI,CAAC;QACb,IAAI,CAAC,YAAY,CAAC;QAElB,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,QAAQ;QAE3C,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,eAAe,MAAc,EAAqB;QACtD,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;IACpD;IAEA,iCAAiC;IACjC,MAAM,kBAAkB,MAAc,EAAqB;QACzD,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;QAC1C,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,MAAM,KAAK,aAClB,CAAC,CAAC,OAAO,SAAS,IAAI,IAAI,KAAK,OAAO,SAAS,IAAI,IAAI,MAAM;IAEjE;IAEA,iBAAiB;IACjB,MAAM,YAAY,QAAgB,EAAE,MAAc,EAAmB;QACnE,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE,MAAM,KAAK;QAE7E,IAAI,gBAAgB,CAAC,GAAG;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,OAAO,CAAC,YAAY;QAEnC,IAAI,OAAO,MAAM,KAAK,WAAW;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,SAAS,IAAI,IAAI,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ;YAChE,OAAO,MAAM,GAAG;YAChB,IAAI,CAAC,YAAY,CAAC;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,GAAG;QAChB,OAAO,SAAS,GAAG,IAAI;QAEvB,IAAI,CAAC,YAAY,CAAC;QAElB,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,OAAO,MAAM;QAEnD,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,MAAc,EAAiD;QACnF,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,UAAoB,EAAE;QAC5B,IAAI,QAAQ;QAEZ,KAAK,MAAM,UAAU,eAAgB;YACnC,IAAI;gBACF,MAAM,gBAAgB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE;gBACxD,QAAQ,IAAI,CAAC;gBACb,SAAS,cAAc,MAAM;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;YACxD;QACF;QAEA,OAAO;YAAE;YAAS;QAAM;IAC1B;IAEA,iCAAiC;IACjC,MAAM,YAAY,MAAc,EAAE,IAAgB,EAAE,QAA8B,EAA0B;QAC1G,MAAM,QAAQ,IAAI,CAAC,eAAe;QAClC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,EAAE,QAAQ;QAE1D,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,MAAM;YAC3D,OAAO;QACT;QAEA,wBAAwB;QACxB,IAAI,KAAK,cAAc,EAAE;YACvB,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;YAC9C,MAAM,aAAa,YAChB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MACzB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,GAAG,CAAC,EAAE;YAEvF,IAAI,YAAY;gBACd,MAAM,sBAAsB,KAAK,GAAG,KAAK,IAAI,KAAK,WAAW,SAAS,EAAE,OAAO;gBAC/E,MAAM,aAAa,KAAK,cAAc,GAAG,KAAK,KAAK;gBAEnD,IAAI,sBAAsB,YAAY;oBACpC,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,MAAM;oBAC9D,OAAO;gBACT;YACF;QACF;QAEA,mBAAmB;QACnB,IAAI,KAAK,SAAS,EAAE;YAClB,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;YAC9C,MAAM,cAAc,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,MAAM,MAAM;YAErE,IAAI,eAAe,KAAK,SAAS,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,MAAM;gBACzD,OAAO;YACT;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,MAAM,KAAK,MAAM,EAAE,KAAK,WAAW,EAAE;IAC9E;IAEA,6BAA6B;IAC7B,MAAM,aAAa,MAAc,EAA4B;QAC3D,MAAM,WAAW,IAAI,CAAC,aAAa;QACnC,IAAI,YAAY,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAEhD,IAAI,CAAC,WAAW;YACd,YAAY;gBACV;gBACA,aAAa;gBACb,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,cAAc,EAAE;YAClB;YACA,SAAS,IAAI,CAAC;YACd,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,+BAA+B;QAC/B,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,UAAU,cAAc,GAAG,eAAe,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QAEvF,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAc,gBAAgB,MAAc,EAAE,YAAoB,EAAE,aAAqB,EAAiB;QACxG,MAAM,WAAW,IAAI,CAAC,aAAa;QACnC,IAAI,iBAAiB,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAE1D,IAAI,mBAAmB,CAAC,GAAG;YACzB,SAAS,IAAI,CAAC;gBACZ;gBACA,aAAa;gBACb,cAAc;gBACd,gBAAgB;gBAChB,YAAY;gBACZ,cAAc,EAAE;YAClB;QACF,OAAO;YACL,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI;YACxC,QAAQ,CAAC,eAAe,CAAC,YAAY,IAAI;YAEzC,IAAI,gBAAgB,GAAG;gBACrB,QAAQ,CAAC,eAAe,CAAC,aAAa,GAAG,IAAI;YAC/C;QACF;QAEA,IAAI,CAAC,cAAc,CAAC;IACtB;IAEA,mBAAmB;IACnB,MAAM,iBAAwC;QAC5C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA,kBAAkB;IAClB,MAAM,eAAe,QAAgB,EAAE,EAA8B;QACnE,MAAM,WAAW,IAAI,CAAC,aAAa;QACnC,OAAO,SACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAC9C,KAAK,CAAC,GAAG;IACd;AACF;AAEO,MAAM,eAAe,IAAI;uCACjB", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/api/community/posts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { db } from '@/lib/database';\nimport { communityService } from '@/lib/community';\nimport { rewardSystem } from '@/lib/rewards';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const category = searchParams.get('category');\n    const type = searchParams.get('type');\n    const authorId = searchParams.get('authorId');\n\n    const filters: any = {};\n    if (category) filters.category = category;\n    if (type) filters.type = type;\n    if (authorId) filters.authorId = authorId;\n\n    const posts = await communityService.getPosts(filters);\n    \n    // Enrich posts with author information\n    const enrichedPosts = await Promise.all(\n      posts.map(async (post) => {\n        const author = await db.getUserById(post.authorId);\n        return {\n          ...post,\n          author: author ? {\n            id: author.id,\n            name: author.name,\n            avatar: author.avatar,\n          } : null,\n        };\n      })\n    );\n    \n    return NextResponse.json({ posts: enrichedPosts });\n  } catch (error: any) {\n    console.error('Get posts error:', error);\n    \n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const token = request.cookies.get('auth-token')?.value;\n\n    if (!token) {\n      return NextResponse.json(\n        { error: 'No authentication token found' },\n        { status: 401 }\n      );\n    }\n\n    // Verify token\n    const decoded = db.verifyToken(token);\n    if (!decoded) {\n      return NextResponse.json(\n        { error: 'Invalid or expired token' },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const {\n      title,\n      content,\n      type,\n      category,\n      tags,\n      images,\n    } = body;\n\n    if (!title || !content || !type || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    const postData = {\n      authorId: decoded.userId,\n      title,\n      content,\n      type,\n      category,\n      tags: tags || [],\n      images: images || [],\n      isSticky: false,\n      status: 'published' as const,\n    };\n\n    const post = await communityService.createPost(postData);\n\n    // Award community participation reward for creating posts\n    try {\n      await rewardSystem.awardReward(decoded.userId, 'community_participation', {\n        action: 'create_post',\n        postId: post.id,\n        postTitle: post.title,\n      });\n    } catch (rewardError) {\n      console.error('Failed to award community participation reward:', rewardError);\n      // Don't fail post creation if reward fails\n    }\n\n    // Get author information for response\n    const author = await db.getUserById(decoded.userId);\n    const enrichedPost = {\n      ...post,\n      author: author ? {\n        id: author.id,\n        name: author.name,\n        avatar: author.avatar,\n      } : null,\n    };\n\n    return NextResponse.json({\n      message: 'Post created successfully',\n      post: enrichedPost,\n    });\n  } catch (error: any) {\n    console.error('Create post error:', error);\n    \n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,MAAM,UAAe,CAAC;QACtB,IAAI,UAAU,QAAQ,QAAQ,GAAG;QACjC,IAAI,MAAM,QAAQ,IAAI,GAAG;QACzB,IAAI,UAAU,QAAQ,QAAQ,GAAG;QAEjC,MAAM,QAAQ,MAAM,yHAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC;QAE9C,uCAAuC;QACvC,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,MAAM,GAAG,CAAC,OAAO;YACf,MAAM,SAAS,MAAM,wHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,KAAK,QAAQ;YACjD,OAAO;gBACL,GAAG,IAAI;gBACP,QAAQ,SAAS;oBACf,IAAI,OAAO,EAAE;oBACb,MAAM,OAAO,IAAI;oBACjB,QAAQ,OAAO,MAAM;gBACvB,IAAI;YACN;QACF;QAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAc;IAClD,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oBAAoB;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgC,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,UAAU,wHAAA,CAAA,KAAE,CAAC,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,MAAM,EACP,GAAG;QAEJ,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;YACf,UAAU,QAAQ,MAAM;YACxB;YACA;YACA;YACA;YACA,MAAM,QAAQ,EAAE;YAChB,QAAQ,UAAU,EAAE;YACpB,UAAU;YACV,QAAQ;QACV;QAEA,MAAM,OAAO,MAAM,yHAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC;QAE/C,0DAA0D;QAC1D,IAAI;YACF,MAAM,uHAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE,2BAA2B;gBACxE,QAAQ;gBACR,QAAQ,KAAK,EAAE;gBACf,WAAW,KAAK,KAAK;YACvB;QACF,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,mDAAmD;QACjE,2CAA2C;QAC7C;QAEA,sCAAsC;QACtC,MAAM,SAAS,MAAM,wHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,QAAQ,MAAM;QAClD,MAAM,eAAe;YACnB,GAAG,IAAI;YACP,QAAQ,SAAS;gBACf,IAAI,OAAO,EAAE;gBACb,MAAM,OAAO,IAAI;gBACjB,QAAQ,OAAO,MAAM;YACvB,IAAI;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sBAAsB;QAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}