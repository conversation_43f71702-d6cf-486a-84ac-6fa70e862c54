import { useState, useCallback } from 'react';
import { useAccount, useChainId, useWriteContract, useReadContract, useWaitForTransactionReceipt } from 'wagmi';
import { parseEther, formatEther } from 'viem';
import { CONTRACT_ABIS, getContractAddress, TransactionType, Web3Error } from '@/lib/web3';

export function useWeb3() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { writeContract } = useWriteContract();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function to handle contract writes
  const executeTransaction = useCallback(async (
    contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',
    functionName: string,
    args: any[] = [],
    value?: bigint
  ) => {
    if (!isConnected || !address) {
      throw new Web3Error('Wallet not connected');
    }

    setIsLoading(true);
    setError(null);

    try {
      const contractAddress = getContractAddress(contractName, chainId);
      let abi;

      switch (contractName) {
        case 'NFTi':
        case 'NFTr':
          abi = CONTRACT_ABIS.NFT;
          break;
        case 'MLIFE_TOKEN':
          abi = CONTRACT_ABIS.ERC20;
          break;
        case 'MARKETPLACE':
          abi = CONTRACT_ABIS.MARKETPLACE;
          break;
        case 'PROPERTY_REGISTRY':
          abi = CONTRACT_ABIS.PROPERTY_REGISTRY;
          break;
        default:
          throw new Web3Error('Unknown contract');
      }

      const result = await writeContract({
        address: contractAddress as `0x${string}`,
        abi,
        functionName,
        args,
        value,
      });

      return result;
    } catch (err: any) {
      const errorMessage = err.message || 'Transaction failed';
      setError(errorMessage);
      throw new Web3Error(errorMessage, err.code, err.data);
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, address, chainId, writeContract]);

  return {
    address,
    isConnected,
    chainId,
    isLoading,
    error,
    executeTransaction,
    clearError: () => setError(null),
  };
}

// Hook for reading contract data
export function useContractRead(
  contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',
  functionName: string,
  args: any[] = []
) {
  const chainId = useChainId();
  
  const contractAddress = getContractAddress(contractName, chainId);
  let abi;

  switch (contractName) {
    case 'NFTi':
    case 'NFTr':
      abi = CONTRACT_ABIS.NFT;
      break;
    case 'MLIFE_TOKEN':
      abi = CONTRACT_ABIS.ERC20;
      break;
    case 'MARKETPLACE':
      abi = CONTRACT_ABIS.MARKETPLACE;
      break;
    case 'PROPERTY_REGISTRY':
      abi = CONTRACT_ABIS.PROPERTY_REGISTRY;
      break;
    default:
      throw new Error('Unknown contract');
  }

  return useReadContract({
    address: contractAddress as `0x${string}`,
    abi,
    functionName,
    args,
  });
}

// Hook for $MLIFE token operations
export function useMLifeToken() {
  const { executeTransaction } = useWeb3();
  const { address } = useAccount();
  const chainId = useChainId();

  // Get token balance
  const { data: balance, refetch: refetchBalance } = useContractRead(
    'MLIFE_TOKEN',
    'balanceOf',
    address ? [address] : []
  );

  // Transfer tokens
  const transfer = useCallback(async (to: string, amount: string) => {
    const amountWei = parseEther(amount);
    return executeTransaction('MLIFE_TOKEN', 'transfer', [to, amountWei]);
  }, [executeTransaction]);

  // Approve tokens
  const approve = useCallback(async (spender: string, amount: string) => {
    const amountWei = parseEther(amount);
    return executeTransaction('MLIFE_TOKEN', 'approve', [spender, amountWei]);
  }, [executeTransaction]);

  return {
    balance: balance ? formatEther(balance as bigint) : '0',
    transfer,
    approve,
    refetchBalance,
  };
}

// Hook for NFT operations
export function useNFTOperations() {
  const { executeTransaction } = useWeb3();
  const { address } = useAccount();

  // Mint NFTi (Property NFT)
  const mintPropertyNFT = useCallback(async (tokenId: number, tokenURI: string) => {
    if (!address) throw new Web3Error('Wallet not connected');
    return executeTransaction('NFTi', 'mint', [address, tokenId, tokenURI]);
  }, [executeTransaction, address]);

  // Mint NFTr (Rental NFT)
  const mintRentalNFT = useCallback(async (tokenId: number, tokenURI: string) => {
    if (!address) throw new Web3Error('Wallet not connected');
    return executeTransaction('NFTr', 'mint', [address, tokenId, tokenURI]);
  }, [executeTransaction, address]);

  // Transfer NFT
  const transferNFT = useCallback(async (
    contractType: 'NFTi' | 'NFTr',
    from: string,
    to: string,
    tokenId: number
  ) => {
    return executeTransaction(contractType, 'transferFrom', [from, to, tokenId]);
  }, [executeTransaction]);

  // Get NFT owner
  const getNFTOwner = useCallback((contractType: 'NFTi' | 'NFTr', tokenId: number) => {
    return useContractRead(contractType, 'ownerOf', [tokenId]);
  }, []);

  return {
    mintPropertyNFT,
    mintRentalNFT,
    transferNFT,
    getNFTOwner,
  };
}

// Hook for marketplace operations
export function useMarketplace() {
  const { executeTransaction } = useWeb3();
  const chainId = useChainId();

  const { data: rawListings, isLoading } = useContractRead('MARKETPLACE', 'getActiveListings', []);

  // List property for sale/rent
  const listProperty = useCallback(async (
    tokenId: number,
    price: string,
    isForRent: boolean
  ) => {
    const priceWei = parseEther(price);
    return executeTransaction('MARKETPLACE', 'listProperty', [tokenId, priceWei, isForRent]);
  }, [executeTransaction]);

  // Buy property
  const buyProperty = useCallback(async (listingId: number, price: string) => {
    const priceWei = parseEther(price);
    return executeTransaction('MARKETPLACE', 'buyProperty', [listingId], priceWei);
  }, [executeTransaction]);

  // Rent property
  const rentProperty = useCallback(async (
    listingId: number,
    duration: number,
    price: string
  ) => {
    const priceWei = parseEther(price);
    return executeTransaction('MARKETPLACE', 'rentProperty', [listingId, duration], priceWei);
  }, [executeTransaction]);

  // Cancel listing
  const cancelListing = useCallback(async (listingId: number) => {
    return executeTransaction('MARKETPLACE', 'cancelListing', [listingId]);
  }, [executeTransaction]);

  // Get listing details
  const getListing = useCallback((listingId: number) => {
    return useContractRead('MARKETPLACE', 'getListing', [listingId]);
  }, []);

  // Make offer
  const makeOffer = useCallback(async (listingId: number, offerPrice: string) => {
    const offerWei = parseEther(offerPrice);
    return executeTransaction('MARKETPLACE', 'makeOffer', [listingId], offerWei);
  }, [executeTransaction]);

  return {
    properties: rawListings || [],
isLoading,
    listProperty,
    buyProperty,
    rentProperty,
    cancelListing,
    getListing,
    makeOffer,
  };
}

// Hook for property registry operations
export function usePropertyRegistry() {
  const { executeTransaction } = useWeb3();

  // Register new property
  const registerProperty = useCallback(async (
    propertyData: string,
    location: string
  ) => {
    return executeTransaction('PROPERTY_REGISTRY', 'registerProperty', [propertyData, location]);
  }, [executeTransaction]);

  // Tokenize property
  const tokenizeProperty = useCallback(async (
    propertyId: number,
    tokenURI: string
  ) => {
    return executeTransaction('PROPERTY_REGISTRY', 'tokenizeProperty', [propertyId, tokenURI]);
  }, [executeTransaction]);

  // Get property details
  const getProperty = useCallback((propertyId: number) => {
    return useContractRead('PROPERTY_REGISTRY', 'getProperty', [propertyId]);
  }, []);

  // Update property
  const updateProperty = useCallback(async (
    propertyId: number,
    propertyData: string
  ) => {
    return executeTransaction('PROPERTY_REGISTRY', 'updateProperty', [propertyId, propertyData]);
  }, [executeTransaction]);

  return {
    registerProperty,
    tokenizeProperty,
    getProperty,
    updateProperty,
  };
}
