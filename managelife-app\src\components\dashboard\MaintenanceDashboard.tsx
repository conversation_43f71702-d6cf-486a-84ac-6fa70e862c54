import React, { useState } from 'react';
import { Wrench, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getMaintenancePriorityColor } from '@/utils';

interface MaintenanceRequest {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed';
  submittedDate: string;
}

export default function MaintenanceDashboard() {
  const { user } = useAuth();
  const [requests, setRequests] = useState<MaintenanceRequest[]>([]);
  const [newRequest, setNewRequest] = useState({ title: '', description: '', priority: 'medium' });

  const handleSubmitRequest = () => {
    const request = {
      id: Date.now().toString(),
      ...newRequest,
      status: 'pending',
      submittedDate: new Date().toISOString(),
    };
    setRequests([...requests, request]);
    setNewRequest({ title: '', description: '', priority: 'medium' });
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Maintenance Dashboard</h2>
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 mb-8">
        <h3 className="text-lg font-semibold mb-4">Submit New Request</h3>
        <input
          type="text"
          placeholder="Title"
          value={newRequest.title}
          onChange={(e) => setNewRequest({ ...newRequest, title: e.target.value })}
          className="w-full p-2 border mb-2"
        />
        <textarea
          placeholder="Description"
          value={newRequest.description}
          onChange={(e) => setNewRequest({ ...newRequest, description: e.target.value })}
          className="w-full p-2 border mb-2"
        />
        <select
          value={newRequest.priority}
          onChange={(e) => setNewRequest({ ...newRequest, priority: e.target.value as 'low' | 'medium' | 'high' | 'urgent' })}
          className="w-full p-2 border mb-2"
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
          <option value="urgent">Urgent</option>
        </select>
        <button onClick={handleSubmitRequest} className="bg-blue-600 text-white px-4 py-2 rounded-lg">
          Submit Request
        </button>
      </div>
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4">My Maintenance Requests</h3>
        {requests.map((req) => (
          <div key={req.id} className="border-b py-4">
            <div className="flex justify-between">
              <h4 className="font-semibold">{req.title}</h4>
              <span className={getMaintenancePriorityColor(req.priority)}>{req.priority}</span>
            </div>
            <p>{req.description}</p>
            <p>Status: {req.status}</p>
            <p>Submitted: {new Date(req.submittedDate).toLocaleDateString()}</p>
          </div>
        ))}
      </div>
    </div>
  );
}