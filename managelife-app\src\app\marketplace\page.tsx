'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Building2,
  ArrowLeft,
  Grid3X3,
  List,
  SortAsc
} from 'lucide-react';
import Navigation from '@/components/layout/Navigation';
import PropertyFilters from '@/components/marketplace/PropertyFilters';
import PropertyCard from '@/components/marketplace/PropertyCard';

import { useMarketplace } from '@/hooks/useWeb3';


export default function MarketplacePage() {
  const { properties, isLoading } = useMarketplace();
  const [filters, setFilters] = useState({
    searchTerm: '',
    location: '',
    propertyType: '',
    listingType: 'all' as 'all' | 'sale' | 'rent',
    priceRange: { min: 0, max: 10000000 },
    bedrooms: '',
    bathrooms: '',
    isTokenized: null as boolean | null,
    amenities: [] as string[]
  });

  const [favorites, setFavorites] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'price-low' | 'price-high' | 'newest' | 'oldest'>('newest');

  const toggleFavorite = (propertyId: string) => {
    setFavorites(prev =>
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    );
  };

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      location: '',
      propertyType: '',
      listingType: 'all',
      priceRange: { min: 0, max: 10000000 },
      bedrooms: '',
      bathrooms: '',
      isTokenized: null,
      amenities: []
    });
  };

  if (isLoading) return <div>Loading properties...</div>;

  const filteredProperties = (properties || []).filter(property => {
    // Search term filter
    const matchesSearch = !filters.searchTerm ||
      property.title.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      property.city.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      property.description.toLowerCase().includes(filters.searchTerm.toLowerCase());

    // Location filter
    const matchesLocation = !filters.location ||
      property.city.toLowerCase().includes(filters.location.toLowerCase()) ||
      property.state.toLowerCase().includes(filters.location.toLowerCase()) ||
      property.zipCode.includes(filters.location);

    // Property type filter
    const matchesPropertyType = !filters.propertyType || property.propertyType === filters.propertyType;

    // Listing type filter
    const matchesListingType = filters.listingType === 'all' || property.listingType === filters.listingType;

    // Price range filter
    const matchesPrice = property.price >= filters.priceRange.min && property.price <= filters.priceRange.max;

    // Bedrooms filter
    const matchesBedrooms = !filters.bedrooms ||
      (filters.bedrooms === '4' ? property.bedrooms >= 4 : property.bedrooms.toString() === filters.bedrooms);

    // Bathrooms filter
    const matchesBathrooms = !filters.bathrooms ||
      (filters.bathrooms === '4' ? property.bathrooms >= 4 : property.bathrooms.toString() === filters.bathrooms);

    // NFT filter
    const matchesTokenized = filters.isTokenized === null || property.isTokenized === filters.isTokenized;

    // Amenities filter
    const matchesAmenities = filters.amenities.length === 0 ||
      filters.amenities.every(amenity => property.amenities.includes(amenity));

    return matchesSearch && matchesLocation && matchesPropertyType && matchesListingType &&
           matchesPrice && matchesBedrooms && matchesBathrooms && matchesTokenized && matchesAmenities;
  });

  // Sort properties
  if (isLoading) return <div>Loading properties...</div>;

  const sortedProperties = [...(filteredProperties || [])].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'newest':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Page Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Property Marketplace</h1>
              <p className="text-gray-600 mt-2">Discover tokenized real estate opportunities</p>
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{(properties || []).length}</p>
                <p>Properties</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">{(properties || []).filter(p => p.isTokenized).length}</p>
                <p>NFT Properties</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{(properties || []).filter(p => p.listingType === 'sale').length}</p>
                <p>For Sale</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <PropertyFilters
        filters={filters}
        onFiltersChange={setFilters}
        onClearFilters={clearFilters}
      />

      {/* Results Header */}
      <section className="bg-white border-b border-gray-200 py-4">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <p className="text-gray-600">
                <span className="font-semibold text-gray-900">{sortedProperties.length}</span> properties found
              </p>
              {filters.searchTerm && (
                <p className="text-sm text-gray-500">
                  for "{filters.searchTerm}"
                </p>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
              </select>

              {/* View Mode */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Properties Grid/List */}
      <section className="py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {sortedProperties.length > 0 ? (
            <div className={viewMode === 'grid'
              ? 'grid md:grid-cols-2 lg:grid-cols-3 gap-8'
              : 'space-y-6'
            }>
              {sortedProperties.map((property) => (
                <PropertyCard
                  key={property.id}
                  property={property}
                  onFavoriteToggle={toggleFavorite}
                  isFavorite={favorites.includes(property.id)}
                  showOwnerInfo={viewMode === 'list'}
                  showStats={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No properties found</h3>
              <p className="text-gray-600 mb-4">Try adjusting your search criteria or filters</p>
              <button
                onClick={clearFilters}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          )}

          {/* Load More Button */}
          {sortedProperties.length > 0 && sortedProperties.length >= 12 && (
            <div className="text-center mt-12">
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                Load More Properties
              </button>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
