{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/constants/index.ts"], "sourcesContent": ["import { UserRole } from '@/types';\n\n// Application constants\nexport const APP_NAME = 'ManageLife';\nexport const APP_DESCRIPTION = 'Real Estate Tokenization Platform';\n\n// Supported chains\nexport const SUPPORTED_CHAINS = {\n  ETHEREUM: 1,\n  POLYGON: 137,\n  SEPOLIA: 11155111, // Testnet\n} as const;\n\n// Contract addresses (placeholder - replace with actual deployed contracts)\nexport const CONTRACT_ADDRESSES = {\n  NFTi: '******************************************',\n  NFTr: '******************************************',\n  MLIFE_TOKEN: '******************************************',\n  MARKETPLACE: '******************************************',\n  REWARDS: '******************************************',\n} as const;\n\n// User roles\nexport const USER_ROLES: UserRole[] = [\n  'homeowner',\n  'renter',\n  'buyer',\n  'portfolio-manager',\n  'community-member',\n];\n\n// Role permissions\nexport const ROLE_PERMISSIONS = {\n  homeowner: [\n    'create_property',\n    'tokenize_property',\n    'list_property',\n    'manage_leases',\n    'view_analytics',\n    'request_maintenance',\n  ],\n  renter: [\n    'browse_properties',\n    'create_lease',\n    'pay_rent',\n    'request_maintenance',\n    'view_lease_history',\n  ],\n  buyer: [\n    'browse_marketplace',\n    'purchase_property',\n    'make_offers',\n    'view_property_details',\n    'save_favorites',\n  ],\n  'portfolio-manager': [\n    'manage_multiple_properties',\n    'view_portfolio_analytics',\n    'manage_tenants',\n    'handle_maintenance',\n    'generate_reports',\n  ],\n  'community-member': [\n    'view_events',\n    'participate_discussions',\n    'earn_rewards',\n    'refer_users',\n    'access_resources',\n  ],\n} as const;\n\n// Property types\nexport const PROPERTY_TYPES = [\n  { value: 'house', label: 'House' },\n  { value: 'apartment', label: 'Apartment' },\n  { value: 'condo', label: 'Condominium' },\n  { value: 'commercial', label: 'Commercial' },\n] as const;\n\n// Property statuses\nexport const PROPERTY_STATUSES = [\n  { value: 'available', label: 'Available', color: 'green' },\n  { value: 'rented', label: 'Rented', color: 'blue' },\n  { value: 'sold', label: 'Sold', color: 'gray' },\n  { value: 'maintenance', label: 'Under Maintenance', color: 'yellow' },\n] as const;\n\n// Currencies\nexport const CURRENCIES = [\n  { value: 'USD', label: 'US Dollar', symbol: '$' },\n  { value: 'ETH', label: 'Ethereum', symbol: 'Ξ' },\n  { value: 'MLIFE', label: 'ManageLife Token', symbol: '$MLIFE' },\n] as const;\n\n// Maintenance categories\nexport const MAINTENANCE_CATEGORIES = [\n  { value: 'plumbing', label: 'Plumbing' },\n  { value: 'electrical', label: 'Electrical' },\n  { value: 'hvac', label: 'HVAC' },\n  { value: 'appliance', label: 'Appliance' },\n  { value: 'structural', label: 'Structural' },\n  { value: 'other', label: 'Other' },\n] as const;\n\n// Maintenance priorities\nexport const MAINTENANCE_PRIORITIES = [\n  { value: 'low', label: 'Low', color: 'green' },\n  { value: 'medium', label: 'Medium', color: 'yellow' },\n  { value: 'high', label: 'High', color: 'orange' },\n  { value: 'urgent', label: 'Urgent', color: 'red' },\n] as const;\n\n// Payment statuses\nexport const PAYMENT_STATUSES = [\n  { value: 'pending', label: 'Pending', color: 'yellow' },\n  { value: 'paid', label: 'Paid', color: 'green' },\n  { value: 'overdue', label: 'Overdue', color: 'red' },\n] as const;\n\n// Reward sources\nexport const REWARD_SOURCES = [\n  { value: 'rent_payment', label: 'Rent Payment' },\n  { value: 'referral', label: 'Referral' },\n  { value: 'community_activity', label: 'Community Activity' },\n  { value: 'staking', label: 'Staking' },\n  { value: 'marketplace_activity', label: 'Marketplace Activity' },\n] as const;\n\n// Event types\nexport const EVENT_TYPES = [\n  { value: 'webinar', label: 'Webinar' },\n  { value: 'meetup', label: 'Meetup' },\n  { value: 'workshop', label: 'Workshop' },\n  { value: 'announcement', label: 'Announcement' },\n] as const;\n\n// Social media links\nexport const SOCIAL_LINKS = {\n  TELEGRAM: 'https://t.me/managelife',\n  DISCORD: 'https://discord.gg/managelife',\n  TWITTER: 'https://twitter.com/managelife',\n  LINKEDIN: 'https://linkedin.com/company/managelife',\n  MEDIUM: 'https://medium.com/@managelife',\n} as const;\n\n// API endpoints\nexport const API_ENDPOINTS = {\n  PROPERTIES: '/api/properties',\n  USERS: '/api/users',\n  LEASES: '/api/leases',\n  MARKETPLACE: '/api/marketplace',\n  REWARDS: '/api/rewards',\n  MAINTENANCE: '/api/maintenance',\n  EVENTS: '/api/events',\n  AUTH: '/api/auth',\n} as const;\n\n// Local storage keys\nexport const STORAGE_KEYS = {\n  USER_PREFERENCES: 'managelife_user_preferences',\n  WALLET_CONNECTION: 'managelife_wallet_connection',\n  THEME: 'managelife_theme',\n  LANGUAGE: 'managelife_language',\n} as const;\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 10,\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// File upload\nexport const FILE_UPLOAD = {\n  MAX_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],\n  MAX_FILES: 10,\n} as const;\n\n// Validation rules\nexport const VALIDATION = {\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  DESCRIPTION_MAX_LENGTH: 1000,\n  TITLE_MAX_LENGTH: 100,\n} as const;\n\n// Theme colors\nexport const THEME_COLORS = {\n  PRIMARY: {\n    50: '#eff6ff',\n    100: '#dbeafe',\n    500: '#3b82f6',\n    600: '#2563eb',\n    700: '#1d4ed8',\n    900: '#1e3a8a',\n  },\n  SECONDARY: {\n    50: '#faf5ff',\n    100: '#f3e8ff',\n    500: '#8b5cf6',\n    600: '#7c3aed',\n    700: '#6d28d9',\n    900: '#4c1d95',\n  },\n} as const;\n\n// Dashboard navigation items\nexport const DASHBOARD_NAV_ITEMS = {\n  homeowner: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home' },\n    { href: '/dashboard/properties', label: 'My Properties', icon: 'Building' },\n    { href: '/rentals', label: 'Rental Management', icon: 'FileText' },\n    { href: '/dashboard/nfts', label: 'My NFTs', icon: 'Coins' },\n    { href: '/notifications', label: 'Notifications', icon: 'Bell' },\n    { href: '/dashboard/rewards', label: 'Rewards', icon: 'Gift' },\n    { href: '/dashboard/analytics', label: 'Analytics', icon: 'BarChart3' },\n  ],\n  renter: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home' },\n    { href: '/rentals', label: 'My Rental', icon: 'FileText' },\n    { href: '/dashboard/payments', label: 'Payments', icon: 'CreditCard' },\n    { href: '/dashboard/maintenance', label: 'Maintenance', icon: 'Wrench' },\n    { href: '/notifications', label: 'Notifications', icon: 'Bell' },\n    { href: '/dashboard/rewards', label: 'Rewards', icon: 'Gift' },\n  ],\n  buyer: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home' },\n    { href: '/marketplace', label: 'Browse Properties', icon: 'Search' },\n    { href: '/dashboard/favorites', label: 'Favorites', icon: 'Heart' },\n    { href: '/dashboard/offers', label: 'My Offers', icon: 'HandCoins' },\n    { href: '/notifications', label: 'Notifications', icon: 'Bell' },\n    { href: '/dashboard/purchases', label: 'Purchases', icon: 'ShoppingCart' },\n  ],\n  'portfolio-manager': [\n    { href: '/dashboard', label: 'Overview', icon: 'Home' },\n    { href: '/portfolio', label: 'Portfolio', icon: 'Building2' },\n    { href: '/dashboard/tenants', label: 'Tenants', icon: 'Users' },\n    { href: '/dashboard/maintenance', label: 'Maintenance', icon: 'Wrench' },\n    { href: '/notifications', label: 'Notifications', icon: 'Bell' },\n    { href: '/dashboard/reports', label: 'Reports', icon: 'FileBarChart' },\n    { href: '/dashboard/analytics', label: 'Analytics', icon: 'BarChart3' },\n  ],\n  'community-member': [\n    { href: '/dashboard', label: 'Overview', icon: 'Home' },\n    { href: '/community', label: 'Community', icon: 'Users' },\n    { href: '/dashboard/events', label: 'Events', icon: 'Calendar' },\n    { href: '/notifications', label: 'Notifications', icon: 'Bell' },\n    { href: '/dashboard/rewards', label: 'Rewards', icon: 'Gift' },\n    { href: '/dashboard/referrals', label: 'Referrals', icon: 'UserPlus' },\n  ],\n} as const;\n\n// Utility functions for roles\nexport function getRoleDisplayName(role: string): string {\n  switch (role) {\n    case 'homeowner':\n      return 'Homeowner';\n    case 'renter':\n      return 'Renter';\n    case 'buyer':\n      return 'Buyer';\n    case 'portfolio-manager':\n      return 'Portfolio Manager';\n    case 'community-member':\n      return 'Community Member';\n    default:\n      return role;\n  }\n}\n\nexport function getRoleColor(role: string): string {\n  switch (role) {\n    case 'homeowner':\n      return 'text-blue-600 bg-blue-100';\n    case 'renter':\n      return 'text-green-600 bg-green-100';\n    case 'buyer':\n      return 'text-purple-600 bg-purple-100';\n    case 'portfolio-manager':\n      return 'text-orange-600 bg-orange-100';\n    case 'community-member':\n      return 'text-pink-600 bg-pink-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAM,WAAW;AACjB,MAAM,kBAAkB;AAGxB,MAAM,mBAAmB;IAC9B,UAAU;IACV,SAAS;IACT,SAAS;AACX;AAGO,MAAM,qBAAqB;IAChC,MAAM;IACN,MAAM;IACN,aAAa;IACb,aAAa;IACb,SAAS;AACX;AAGO,MAAM,aAAyB;IACpC;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,mBAAmB;IAC9B,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;IACD,qBAAqB;QACnB;QACA;QACA;QACA;QACA;KACD;IACD,oBAAoB;QAClB;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAS,OAAO;IAAc;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAGM,MAAM,oBAAoB;IAC/B;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAQ;IACzD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAO;IAClD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAO;IAC9C;QAAE,OAAO;QAAe,OAAO;QAAqB,OAAO;IAAS;CACrE;AAGM,MAAM,aAAa;IACxB;QAAE,OAAO;QAAO,OAAO;QAAa,QAAQ;IAAI;IAChD;QAAE,OAAO;QAAO,OAAO;QAAY,QAAQ;IAAI;IAC/C;QAAE,OAAO;QAAS,OAAO;QAAoB,QAAQ;IAAS;CAC/D;AAGM,MAAM,yBAAyB;IACpC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAGM,MAAM,yBAAyB;IACpC;QAAE,OAAO;QAAO,OAAO;QAAO,OAAO;IAAQ;IAC7C;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAS;IACpD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAS;IAChD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAM;CAClD;AAGM,MAAM,mBAAmB;IAC9B;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAS;IACtD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAM;CACpD;AAGM,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAsB,OAAO;IAAqB;IAC3D;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAwB,OAAO;IAAuB;CAChE;AAGM,MAAM,cAAc;IACzB;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAGM,MAAM,eAAe;IAC1B,UAAU;IACV,SAAS;IACT,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAGO,MAAM,gBAAgB;IAC3B,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,aAAa;IACb,SAAS;IACT,aAAa;IACb,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,eAAe;IAC1B,kBAAkB;IAClB,mBAAmB;IACnB,OAAO;IACP,UAAU;AACZ;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,eAAe;AACjB;AAGO,MAAM,cAAc;IACzB,UAAU,KAAK,OAAO;IACtB,eAAe;QAAC;QAAc;QAAa;QAAc;KAAkB;IAC3E,WAAW;AACb;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,wBAAwB;IACxB,kBAAkB;AACpB;AAGO,MAAM,eAAe;IAC1B,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,sBAAsB;IACjC,WAAW;QACT;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;QAAO;QACtD;YAAE,MAAM;YAAyB,OAAO;YAAiB,MAAM;QAAW;QAC1E;YAAE,MAAM;YAAY,OAAO;YAAqB,MAAM;QAAW;QACjE;YAAE,MAAM;YAAmB,OAAO;YAAW,MAAM;QAAQ;QAC3D;YAAE,MAAM;YAAkB,OAAO;YAAiB,MAAM;QAAO;QAC/D;YAAE,MAAM;YAAsB,OAAO;YAAW,MAAM;QAAO;QAC7D;YAAE,MAAM;YAAwB,OAAO;YAAa,MAAM;QAAY;KACvE;IACD,QAAQ;QACN;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;QAAO;QACtD;YAAE,MAAM;YAAY,OAAO;YAAa,MAAM;QAAW;QACzD;YAAE,MAAM;YAAuB,OAAO;YAAY,MAAM;QAAa;QACrE;YAAE,MAAM;YAA0B,OAAO;YAAe,MAAM;QAAS;QACvE;YAAE,MAAM;YAAkB,OAAO;YAAiB,MAAM;QAAO;QAC/D;YAAE,MAAM;YAAsB,OAAO;YAAW,MAAM;QAAO;KAC9D;IACD,OAAO;QACL;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;QAAO;QACtD;YAAE,MAAM;YAAgB,OAAO;YAAqB,MAAM;QAAS;QACnE;YAAE,MAAM;YAAwB,OAAO;YAAa,MAAM;QAAQ;QAClE;YAAE,MAAM;YAAqB,OAAO;YAAa,MAAM;QAAY;QACnE;YAAE,MAAM;YAAkB,OAAO;YAAiB,MAAM;QAAO;QAC/D;YAAE,MAAM;YAAwB,OAAO;YAAa,MAAM;QAAe;KAC1E;IACD,qBAAqB;QACnB;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;QAAO;QACtD;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM;QAAY;QAC5D;YAAE,MAAM;YAAsB,OAAO;YAAW,MAAM;QAAQ;QAC9D;YAAE,MAAM;YAA0B,OAAO;YAAe,MAAM;QAAS;QACvE;YAAE,MAAM;YAAkB,OAAO;YAAiB,MAAM;QAAO;QAC/D;YAAE,MAAM;YAAsB,OAAO;YAAW,MAAM;QAAe;QACrE;YAAE,MAAM;YAAwB,OAAO;YAAa,MAAM;QAAY;KACvE;IACD,oBAAoB;QAClB;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;QAAO;QACtD;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM;QAAQ;QACxD;YAAE,MAAM;YAAqB,OAAO;YAAU,MAAM;QAAW;QAC/D;YAAE,MAAM;YAAkB,OAAO;YAAiB,MAAM;QAAO;QAC/D;YAAE,MAAM;YAAsB,OAAO;YAAW,MAAM;QAAO;QAC7D;YAAE,MAAM;YAAwB,OAAO;YAAa,MAAM;QAAW;KACtE;AACH;AAGO,SAAS,mBAAmB,IAAY;IAC7C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,IAAY;IACvC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Building2, Mail, Lock, User, ArrowLeft, Check, AlertCircle } from 'lucide-react';\nimport { UserRole } from '@/types';\nimport { USER_ROLES, getRoleDisplayName, getRoleColor } from '@/constants';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function RegisterPage() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);\n  const [step, setStep] = useState<'info' | 'roles' | 'verification'>('info');\n  const [error, setError] = useState('');\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n\n  const router = useRouter();\n  const { register } = useAuth();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value,\n    }));\n    setError(''); // Clear error when user types\n  };\n\n  const handleRoleToggle = (role: UserRole) => {\n    setSelectedRoles(prev =>\n      prev.includes(role)\n        ? prev.filter(r => r !== role)\n        : [...prev, role]\n    );\n  };\n\n  const handleInfoSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setError('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (formData.password.length < 8) {\n      setError('Password must be at least 8 characters long');\n      return;\n    }\n\n    setStep('roles');\n  };\n\n  const handleRoleSubmit = () => {\n    if (selectedRoles.length === 0) {\n      setError('Please select at least one role');\n      return;\n    }\n    setError('');\n    setStep('verification');\n  };\n\n  const handleFinalSubmit = async () => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      await register({\n        ...formData,\n        roles: selectedRoles,\n      });\n      router.push('/dashboard?welcome=true');\n    } catch (error: any) {\n      setError(error.message || 'Registration failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        {/* Back to Home */}\n        <Link\n          href=\"/\"\n          className=\"inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors mb-8\"\n        >\n          <ArrowLeft className=\"w-4 h-4 mr-2\" />\n          Back to Home\n        </Link>\n\n        {/* Registration Card */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <Building2 className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold gradient-text\">ManageLife</span>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Create Account</h1>\n            <p className=\"text-gray-600\">\n              {step === 'info' && 'Enter your information to get started'}\n              {step === 'roles' && 'Select your roles on the platform'}\n              {step === 'verification' && 'Complete your registration'}\n            </p>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n              <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n              <p className=\"text-red-700 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Progress Indicator */}\n          <div className=\"flex items-center justify-center mb-8\">\n            <div className=\"flex items-center space-x-4\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                step === 'info' ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'\n              }`}>\n                {step === 'info' ? '1' : <Check className=\"w-4 h-4\" />}\n              </div>\n              <div className={`w-16 h-1 ${step === 'verification' ? 'bg-green-600' : step === 'roles' ? 'bg-blue-600' : 'bg-gray-300'}`}></div>\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                step === 'info' ? 'bg-gray-300 text-gray-600' : \n                step === 'roles' ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'\n              }`}>\n                {step === 'verification' ? <Check className=\"w-4 h-4\" /> : '2'}\n              </div>\n              <div className={`w-16 h-1 ${step === 'verification' ? 'bg-blue-600' : 'bg-gray-300'}`}></div>\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                step === 'verification' ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'\n              }`}>\n                3\n              </div>\n            </div>\n          </div>\n\n          {/* Step 1: Basic Information */}\n          {step === 'info' && (\n            <form onSubmit={handleInfoSubmit} className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Full Name\n                </label>\n                <div className=\"relative\">\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"name\"\n                    name=\"name\"\n                    type=\"text\"\n                    required\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Enter your full name\"\n                  />\n                </div>\n              </div>\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Enter your email\"\n                  />\n                </div>\n              </div>\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type=\"password\"\n                    required\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Create a password\"\n                  />\n                </div>\n              </div>\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Confirm Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type=\"password\"\n                    required\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Confirm your password\"\n                  />\n                </div>\n              </div>\n              <button\n                type=\"submit\"\n                className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n              >\n                Continue\n              </button>\n            </form>\n          )}\n\n          {/* Step 2: Role Selection */}\n          {step === 'roles' && (\n            <div className=\"space-y-4\">\n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Choose Your Roles</h3>\n                <p className=\"text-gray-600 text-sm\">Select one or more roles that describe how you'll use ManageLife</p>\n              </div>\n              <div className=\"space-y-3\">\n                {USER_ROLES.map((role) => (\n                  <button\n                    key={role}\n                    onClick={() => handleRoleToggle(role)}\n                    className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${\n                      selectedRoles.includes(role)\n                        ? 'border-blue-500 bg-blue-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">{getRoleDisplayName(role)}</h4>\n                        <p className=\"text-sm text-gray-600 mt-1\">\n                          {role === 'homeowner' && 'Own and tokenize properties, manage rentals'}\n                          {role === 'renter' && 'Rent properties, pay with crypto, earn rewards'}\n                          {role === 'buyer' && 'Purchase tokenized properties and NFTs'}\n                          {role === 'portfolio-manager' && 'Manage multiple properties and tenants'}\n                          {role === 'community-member' && 'Participate in community events and earn rewards'}\n                        </p>\n                      </div>\n                      <div className={`w-5 h-5 rounded-full border-2 ${\n                        selectedRoles.includes(role)\n                          ? 'bg-blue-600 border-blue-600'\n                          : 'border-gray-300'\n                      }`}>\n                        {selectedRoles.includes(role) && (\n                          <Check className=\"w-3 h-3 text-white m-0.5\" />\n                        )}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n              <div className=\"flex space-x-4 pt-4\">\n                <button\n                  onClick={() => setStep('info')}\n                  className=\"flex-1 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                >\n                  Back\n                </button>\n                <button\n                  onClick={handleRoleSubmit}\n                  className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n                >\n                  Continue\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Step 3: Verification */}\n          {step === 'verification' && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Complete Registration</h3>\n                <p className=\"text-gray-600 text-sm\">Review your information and complete your account setup</p>\n              </div>\n              \n              <div className=\"bg-gray-50 rounded-lg p-4\">\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Selected Roles:</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {selectedRoles.map((role) => (\n                    <span\n                      key={role}\n                      className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(role)}`}\n                    >\n                      {getRoleDisplayName(role)}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <label className=\"flex items-start\">\n                  <input type=\"checkbox\" required className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n                  <span className=\"ml-3 text-sm text-gray-600\">\n                    I agree to the <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-700\">Terms of Service</Link> and <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-700\">Privacy Policy</Link>\n                  </span>\n                </label>\n                <label className=\"flex items-start\">\n                  <input type=\"checkbox\" className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n                  <span className=\"ml-3 text-sm text-gray-600\">\n                    I want to receive updates about ManageLife features and community events\n                  </span>\n                </label>\n              </div>\n\n              <div className=\"flex space-x-4\">\n                <button\n                  onClick={() => setStep('roles')}\n                  className=\"flex-1 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors\"\n                >\n                  Back\n                </button>\n                <button\n                  onClick={handleFinalSubmit}\n                  disabled={isLoading}\n                  className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50\"\n                >\n                  {isLoading ? 'Creating Account...' : 'Create Account'}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Sign In Link */}\n          <div className=\"text-center mt-6 pt-6 border-t border-gray-200\">\n            <p className=\"text-gray-600\">\n              Already have an account?{' '}\n              <Link href=\"/auth/login\" className=\"text-blue-600 hover:text-blue-700 font-semibold\">\n                Sign in\n              </Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IAEA,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;QACD,SAAS,KAAK,8BAA8B;IAC9C;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,QACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,QACvB;mBAAI;gBAAM;aAAK;IAEvB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,SAAS;QAET,aAAa;QACb,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT;QACF;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,SAAS;YACT;QACF;QAEA,QAAQ;IACV;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,SAAS;YACT;QACF;QACA,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS;gBACb,GAAG,QAAQ;gBACX,OAAO;YACT;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAKxC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAErD,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;;wCACV,SAAS,UAAU;wCACnB,SAAS,WAAW;wCACpB,SAAS,kBAAkB;;;;;;;;;;;;;wBAK/B,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,SAAS,2BAA2B,2BAC7C;kDACC,SAAS,SAAS,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAW,CAAC,SAAS,EAAE,SAAS,iBAAiB,iBAAiB,SAAS,UAAU,gBAAgB,eAAe;;;;;;kDACzH,8OAAC;wCAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,SAAS,8BAClB,SAAS,UAAU,2BAA2B,2BAC9C;kDACC,SAAS,+BAAiB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;mDAAe;;;;;;kDAE7D,8OAAC;wCAAI,WAAW,CAAC,SAAS,EAAE,SAAS,iBAAiB,gBAAgB,eAAe;;;;;;kDACrF,8OAAC;wCAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,iBAAiB,2BAA2B,6BACrD;kDAAE;;;;;;;;;;;;;;;;;wBAOP,SAAS,wBACR,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA+C;;;;;;sDAG1F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,eAAe;oDAC/B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;wBAOJ,SAAS,yBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;8CACZ,yHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC;4CAEC,SAAS,IAAM,iBAAiB;4CAChC,WAAW,CAAC,qEAAqE,EAC/E,cAAc,QAAQ,CAAC,QACnB,+BACA,yCACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA+B,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;0EAChE,8OAAC;gEAAE,WAAU;;oEACV,SAAS,eAAe;oEACxB,SAAS,YAAY;oEACrB,SAAS,WAAW;oEACpB,SAAS,uBAAuB;oEAChC,SAAS,sBAAsB;;;;;;;;;;;;;kEAGpC,8OAAC;wDAAI,WAAW,CAAC,8BAA8B,EAC7C,cAAc,QAAQ,CAAC,QACnB,gCACA,mBACJ;kEACC,cAAc,QAAQ,CAAC,uBACtB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CAzBlB;;;;;;;;;;8CAgCX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,QAAQ;4CACvB,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAQN,SAAS,gCACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAEC,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;8DAE5E,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;mDAHf;;;;;;;;;;;;;;;;8CASb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,MAAK;oDAAW,QAAQ;oDAAC,WAAU;;;;;;8DAC1C,8OAAC;oDAAK,WAAU;;wDAA6B;sEAC5B,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAAoC;;;;;;wDAAuB;sEAAK,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;sDAGrL,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,MAAK;oDAAW,WAAU;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,QAAQ;4CACvB,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,YAAY,wBAAwB;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAgB;oCACF;kDACzB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnG", "debugId": null}}]}