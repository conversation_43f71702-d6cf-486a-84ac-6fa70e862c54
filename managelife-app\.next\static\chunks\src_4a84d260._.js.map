{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/hooks/useCommunity.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { CommunityEvent, CommunityPost, EventType, PostType, PostCategory } from '@/types';\n\nexport function useCommunityEvents() {\n  const [events, setEvents] = useState<CommunityEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchEvents = useCallback(async (filters?: {\n    type?: EventType;\n    status?: string;\n    upcoming?: boolean;\n  }) => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (filters?.type) params.append('type', filters.type);\n      if (filters?.status) params.append('status', filters.status);\n      if (filters?.upcoming) params.append('upcoming', 'true');\n\n      const response = await fetch(`/api/community/events?${params}`);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch events');\n      }\n\n      // Convert date strings back to Date objects\n      const eventsWithDates = data.events.map((event: any) => ({\n        ...event,\n        startDate: new Date(event.startDate),\n        endDate: new Date(event.endDate),\n        createdAt: new Date(event.createdAt),\n        updatedAt: new Date(event.updatedAt),\n      }));\n\n      setEvents(eventsWithDates);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const registerForEvent = useCallback(async (eventId: string) => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/community/events', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'register',\n          eventId,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to register for event');\n      }\n\n      // Refresh events to update participant count\n      await fetchEvents();\n\n      return data.participant;\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchEvents]);\n\n  const createEvent = useCallback(async (eventData: {\n    title: string;\n    description: string;\n    type: EventType;\n    startDate: Date;\n    endDate: Date;\n    location?: string;\n    isVirtual: boolean;\n    maxParticipants?: number;\n    tags?: string[];\n    rewardAmount?: number;\n  }) => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/community/events', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'create',\n          ...eventData,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to create event');\n      }\n\n      // Refresh events\n      await fetchEvents();\n\n      return data.event;\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchEvents]);\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  useEffect(() => {\n    fetchEvents();\n  }, [fetchEvents]);\n\n  return {\n    events,\n    loading,\n    error,\n    fetchEvents,\n    registerForEvent,\n    createEvent,\n    clearError,\n  };\n}\n\nexport function useCommunityPosts() {\n  const [posts, setPosts] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchPosts = useCallback(async (filters?: {\n    category?: PostCategory;\n    type?: PostType;\n    authorId?: string;\n  }) => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (filters?.category) params.append('category', filters.category);\n      if (filters?.type) params.append('type', filters.type);\n      if (filters?.authorId) params.append('authorId', filters.authorId);\n\n      const response = await fetch(`/api/community/posts?${params}`);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch posts');\n      }\n\n      // Convert date strings back to Date objects\n      const postsWithDates = data.posts.map((post: any) => ({\n        ...post,\n        createdAt: new Date(post.createdAt),\n        updatedAt: new Date(post.updatedAt),\n      }));\n\n      setPosts(postsWithDates);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const createPost = useCallback(async (postData: {\n    title: string;\n    content: string;\n    type: PostType;\n    category: PostCategory;\n    tags?: string[];\n    images?: string[];\n  }) => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/community/posts', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(postData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to create post');\n      }\n\n      // Refresh posts\n      await fetchPosts();\n\n      return data.post;\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchPosts]);\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  useEffect(() => {\n    fetchPosts();\n  }, [fetchPosts]);\n\n  return {\n    posts,\n    loading,\n    error,\n    fetchPosts,\n    createPost,\n    clearError,\n  };\n}\n\nexport function useCommunityStats() {\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/community/stats');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch stats');\n      }\n\n      setStats(data.stats);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  return {\n    stats,\n    loading,\n    error,\n    fetchStats,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YAKrC,IAAI;gBACF,WAAW;gBACX,MAAM,SAAS,IAAI;gBAEnB,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;gBACrD,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;gBAC3D,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY;gBAEjD,MAAM,WAAW,MAAM,MAAM,AAAC,yBAA+B,OAAP;gBACtD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,4CAA4C;gBAC5C,MAAM,kBAAkB,KAAK,MAAM,CAAC,GAAG;mFAAC,CAAC,QAAe,CAAC;4BACvD,GAAG,KAAK;4BACR,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,SAAS,IAAI,KAAK,MAAM,OAAO;4BAC/B,WAAW,IAAI,KAAK,MAAM,SAAS;4BACnC,WAAW,IAAI,KAAK,MAAM,SAAS;wBACrC,CAAC;;gBAED,UAAU;YACZ,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;YACtB,SAAU;gBACR,WAAW;YACb;QACF;sDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YAC1C,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM,yBAAyB;oBACpD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;wBACR;oBACF;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,6CAA6C;gBAC7C,MAAM;gBAEN,OAAO,KAAK,WAAW;YACzB,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;gBACpB,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;KAAY;IAEhB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YAYrC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM,yBAAyB;oBACpD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;wBACR,GAAG,SAAS;oBACd;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,iBAAiB;gBACjB,MAAM;gBAEN,OAAO,KAAK,KAAK;YACnB,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;gBACpB,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;sDAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC7B,SAAS;QACX;qDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAtIgB;AAwIT,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YAKpC,IAAI;gBACF,WAAW;gBACX,MAAM,SAAS,IAAI;gBAEnB,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;gBACjE,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;gBACrD,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;gBAEjE,MAAM,WAAW,MAAM,MAAM,AAAC,wBAA8B,OAAP;gBACrD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,4CAA4C;gBAC5C,MAAM,iBAAiB,KAAK,KAAK,CAAC,GAAG;gFAAC,CAAC,OAAc,CAAC;4BACpD,GAAG,IAAI;4BACP,WAAW,IAAI,KAAK,KAAK,SAAS;4BAClC,WAAW,IAAI,KAAK,KAAK,SAAS;wBACpC,CAAC;;gBAED,SAAS;YACX,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;YACtB,SAAU;gBACR,WAAW;YACb;QACF;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YAQpC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM,wBAAwB;oBACnD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,gBAAgB;gBAChB,MAAM;gBAEN,OAAO,KAAK,IAAI;YAClB,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;gBACpB,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;oDAAG;QAAC;KAAW;IAEf,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,SAAS;QACX;oDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA5FgB;AA8FT,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,SAAS,KAAK,KAAK;YACrB,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO;YACtB,SAAU;gBACR,WAAW;YACb;QACF;oDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAjCgB", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n// Utility function for merging Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency values\nexport function formatCurrency(amount: number, currency: 'USD' | 'ETH' | 'MLIFE' = 'USD'): string {\n  switch (currency) {\n    case 'USD':\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n      }).format(amount);\n    case 'ETH':\n      return `${amount.toFixed(4)} ETH`;\n    case 'MLIFE':\n      return `${amount.toLocaleString()} $MLIFE`;\n    default:\n      return amount.toString();\n  }\n}\n\n// Format wallet address\nexport function formatWalletAddress(address: string, chars: number = 4): string {\n  if (!address) return '';\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`;\n}\n\n// Format date\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n}\n\n// Format time only\nexport function formatTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format date with time\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format time ago (relative time)\nexport function formatTimeAgo(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7);\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30);\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365);\n  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;\n}\n\n// Calculate days between dates\nexport function daysBetween(date1: Date, date2: Date): number {\n  const oneDay = 24 * 60 * 60 * 1000;\n  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));\n}\n\n// Check if date is overdue\nexport function isOverdue(dueDate: Date): boolean {\n  return new Date() > new Date(dueDate);\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Validate Ethereum address\nexport function isValidEthereumAddress(address: string): boolean {\n  return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// Convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result as string);\n    reader.onerror = error => reject(error);\n  });\n}\n\n// Get property status color\nexport function getPropertyStatusColor(status: string): string {\n  switch (status) {\n    case 'available':\n      return 'text-green-600 bg-green-100';\n    case 'rented':\n      return 'text-blue-600 bg-blue-100';\n    case 'sold':\n      return 'text-gray-600 bg-gray-100';\n    case 'maintenance':\n      return 'text-yellow-600 bg-yellow-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get maintenance priority color\nexport function getMaintenancePriorityColor(priority: string): string {\n  switch (priority) {\n    case 'urgent':\n      return 'text-red-600 bg-red-100';\n    case 'high':\n      return 'text-orange-600 bg-orange-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get payment status color\nexport function getPaymentStatusColor(status: string): string {\n  switch (status) {\n    case 'paid':\n      return 'text-green-600 bg-green-100';\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'overdue':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Calculate annual yield\nexport function calculateAnnualYield(monthlyRent: number, propertyValue: number): number {\n  if (propertyValue === 0) return 0;\n  return ((monthlyRent * 12) / propertyValue) * 100;\n}\n\n// Format property type\nexport function formatPropertyType(type: string): string {\n  return type.charAt(0).toUpperCase() + type.slice(1);\n}\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAoC;IACjF,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;gBACpC,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QACZ,KAAK;YACH,OAAO,AAAC,GAAoB,OAAlB,OAAO,OAAO,CAAC,IAAG;QAC9B,KAAK;YACH,OAAO,AAAC,GAA0B,OAAxB,OAAO,cAAc,IAAG;QACpC;YACE,OAAO,OAAO,QAAQ;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAe;QAAE,QAAA,iEAAgB;IACnE,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,AAAC,GAAmC,OAAjC,QAAQ,KAAK,CAAC,GAAG,QAAQ,IAAG,OAA2B,OAAtB,QAAQ,KAAK,CAAC,CAAC;AAC5D;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,cAAc,IAAmB;IAC/C,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,AAAC,GAAyB,OAAvB,eAAc,WAAsC,OAA7B,gBAAgB,IAAI,MAAM,IAAG;IAChE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,AAAC,GAAqB,OAAnB,aAAY,SAAkC,OAA3B,cAAc,IAAI,MAAM,IAAG;IAC1D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,AAAC,GAAmB,OAAjB,YAAW,QAAgC,OAA1B,aAAa,IAAI,MAAM,IAAG;IACvD;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,AAAC,GAAqB,OAAnB,aAAY,SAAkC,OAA3B,cAAc,IAAI,MAAM,IAAG;IAC1D;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,AAAC,GAAuB,OAArB,cAAa,UAAoC,OAA5B,eAAe,IAAI,MAAM,IAAG;IAC7D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,AAAC,GAAqB,OAAnB,aAAY,SAAkC,OAA3B,cAAc,IAAI,MAAM,IAAG;AAC1D;AAGO,SAAS,YAAY,KAAW,EAAE,KAAW;IAClD,MAAM,SAAS,KAAK,KAAK,KAAK;IAC9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI;AACnE;AAGO,SAAS,UAAU,OAAa;IACrC,OAAO,IAAI,SAAS,IAAI,KAAK;AAC/B;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,uBAAuB,OAAe;IACpD,OAAO,sBAAsB,IAAI,CAAC;AACpC;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;QAC3C,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,uBAAuB,MAAc;IACnD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,4BAA4B,QAAgB;IAC1D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,sBAAsB,MAAc;IAClD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,qBAAqB,WAAmB,EAAE,aAAqB;IAC7E,IAAI,kBAAkB,GAAG,OAAO;IAChC,OAAO,AAAE,cAAc,KAAM,gBAAiB;AAChD;AAGO,SAAS,mBAAmB,IAAY;IAC7C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/community/CommunityEvents.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Calendar, \n  MapPin, \n  Users, \n  Clock, \n  Video, \n  CheckCircle,\n  AlertCircle,\n  Plus,\n  Filter,\n  Coins\n} from 'lucide-react';\nimport { useCommunityEvents } from '@/hooks/useCommunity';\nimport { formatDate, formatTime } from '@/utils';\nimport { EventType } from '@/types';\n\nexport default function CommunityEvents() {\n  const {\n    events,\n    loading,\n    error,\n    registerForEvent,\n    clearError,\n  } = useCommunityEvents();\n\n  const [registering, setRegistering] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [filter, setFilter] = useState<'all' | 'upcoming' | 'ongoing'>('upcoming');\n\n  const handleRegister = async (eventId: string) => {\n    setRegistering(eventId);\n    try {\n      await registerForEvent(eventId);\n      setSuccess('Successfully registered for event!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to register for event:', error);\n    } finally {\n      setRegistering(null);\n    }\n  };\n\n  const getEventTypeIcon = (type: EventType) => {\n    switch (type) {\n      case 'webinar':\n        return <Video className=\"w-4 h-4\" />;\n      case 'workshop':\n        return <Users className=\"w-4 h-4\" />;\n      case 'networking':\n        return <Users className=\"w-4 h-4\" />;\n      case 'property_tour':\n        return <MapPin className=\"w-4 h-4\" />;\n      default:\n        return <Calendar className=\"w-4 h-4\" />;\n    }\n  };\n\n  const getEventTypeColor = (type: EventType) => {\n    switch (type) {\n      case 'webinar':\n        return 'text-blue-600 bg-blue-100';\n      case 'workshop':\n        return 'text-green-600 bg-green-100';\n      case 'networking':\n        return 'text-purple-600 bg-purple-100';\n      case 'property_tour':\n        return 'text-orange-600 bg-orange-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const filteredEvents = events.filter(event => {\n    const now = new Date();\n    switch (filter) {\n      case 'upcoming':\n        return new Date(event.startDate) > now;\n      case 'ongoing':\n        return new Date(event.startDate) <= now && new Date(event.endDate) >= now;\n      default:\n        return true;\n    }\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n          <Calendar className=\"w-7 h-7 mr-3 text-blue-600\" />\n          Community Events\n        </h2>\n        <div className=\"flex items-center space-x-3\">\n          {/* Filter */}\n          <select\n            value={filter}\n            onChange={(e) => setFilter(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Events</option>\n            <option value=\"upcoming\">Upcoming</option>\n            <option value=\"ongoing\">Ongoing</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n          <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-red-700 text-sm\">{error}</p>\n          <button\n            onClick={clearError}\n            className=\"ml-auto text-red-600 hover:text-red-700\"\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\">\n          <CheckCircle className=\"w-5 h-5 text-green-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-green-700 text-sm\">{success}</p>\n        </div>\n      )}\n\n      {/* Events List */}\n      {loading ? (\n        <div className=\"space-y-4\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"h-6 bg-gray-300 rounded w-2/3 mb-3\"></div>\n                  <div className=\"h-4 bg-gray-300 rounded w-full mb-2\"></div>\n                  <div className=\"h-4 bg-gray-300 rounded w-3/4\"></div>\n                </div>\n                <div className=\"h-10 w-24 bg-gray-300 rounded\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : filteredEvents.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <Calendar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Events Found</h3>\n          <p className=\"text-gray-600\">\n            {filter === 'upcoming' ? 'No upcoming events at the moment.' : 'No events match your filter.'}\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {filteredEvents.map((event) => (\n            <div\n              key={event.id}\n              className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\"\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  {/* Event Header */}\n                  <div className=\"flex items-center space-x-3 mb-3\">\n                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getEventTypeColor(event.type)}`}>\n                      {getEventTypeIcon(event.type)}\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{event.title}</h3>\n                      <span className=\"text-sm text-gray-600 capitalize\">\n                        {event.type.replace('_', ' ')}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Event Description */}\n                  <p className=\"text-gray-600 mb-4 line-clamp-2\">{event.description}</p>\n\n                  {/* Event Details */}\n                  <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Clock className=\"w-4 h-4 mr-2\" />\n                      <span>\n                        {formatDate(event.startDate)} at {formatTime(event.startDate)}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <MapPin className=\"w-4 h-4 mr-2\" />\n                      <span>{event.isVirtual ? 'Virtual Event' : event.location || 'TBD'}</span>\n                    </div>\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Users className=\"w-4 h-4 mr-2\" />\n                      <span>\n                        {event.currentParticipants}\n                        {event.maxParticipants && ` / ${event.maxParticipants}`} participants\n                      </span>\n                    </div>\n                    {event.rewardAmount && (\n                      <div className=\"flex items-center text-sm text-green-600\">\n                        <Coins className=\"w-4 h-4 mr-2\" />\n                        <span>{event.rewardAmount} $MLIFE reward</span>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Tags */}\n                  {event.tags.length > 0 && (\n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {event.tags.map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full\"\n                        >\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n\n                {/* Action Button */}\n                <div className=\"ml-6\">\n                  {event.status === 'upcoming' && (\n                    <button\n                      onClick={() => handleRegister(event.id)}\n                      disabled={\n                        registering === event.id ||\n                        (event.maxParticipants && event.currentParticipants >= event.maxParticipants)\n                      }\n                      className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      {registering === event.id ? 'Registering...' : \n                       event.maxParticipants && event.currentParticipants >= event.maxParticipants ? 'Full' : 'Register'}\n                    </button>\n                  )}\n                  {event.status === 'ongoing' && (\n                    <span className=\"bg-green-100 text-green-800 px-4 py-2 rounded-lg font-medium\">\n                      Ongoing\n                    </span>\n                  )}\n                  {event.status === 'completed' && (\n                    <span className=\"bg-gray-100 text-gray-800 px-4 py-2 rounded-lg font-medium\">\n                      Completed\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAhBA;;;;;AAmBe,SAAS;;IACtB,MAAM,EACJ,MAAM,EACN,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,UAAU,EACX,GAAG,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAErE,MAAM,iBAAiB,OAAO;QAC5B,eAAe;QACf,IAAI;YACF,MAAM,iBAAiB;YACvB,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,MAAM,IAAI;QAChB,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,MAAM,SAAS,IAAI;YACrC,KAAK;gBACH,OAAO,IAAI,KAAK,MAAM,SAAS,KAAK,OAAO,IAAI,KAAK,MAAM,OAAO,KAAK;YACxE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAA+B;;;;;;;kCAGrD,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;;;;;;;;;;;;YAM7B,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMJ,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;YAK1C,wBACC,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;uBAYZ,eAAe,MAAM,KAAK,kBAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCACV,WAAW,aAAa,sCAAsC;;;;;;;;;;;qCAInE,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,uDAAoF,OAA9B,kBAAkB,MAAM,IAAI;8DAChG,iBAAiB,MAAM,IAAI;;;;;;8DAE9B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAuC,MAAM,KAAK;;;;;;sEAChE,6LAAC;4DAAK,WAAU;sEACb,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;sDAM/B,6LAAC;4CAAE,WAAU;sDAAmC,MAAM,WAAW;;;;;;sDAGjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEACE,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;gEAAE;gEAAK,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAM,MAAM,SAAS,GAAG,kBAAkB,MAAM,QAAQ,IAAI;;;;;;;;;;;;8DAE/D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEACE,MAAM,mBAAmB;gEACzB,MAAM,eAAe,IAAI,AAAC,MAA2B,OAAtB,MAAM,eAAe;gEAAG;;;;;;;;;;;;;gDAG3D,MAAM,YAAY,kBACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEAAM,MAAM,YAAY;gEAAC;;;;;;;;;;;;;;;;;;;wCAM/B,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACpB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;8CAWf,6LAAC;oCAAI,WAAU;;wCACZ,MAAM,MAAM,KAAK,4BAChB,6LAAC;4CACC,SAAS,IAAM,eAAe,MAAM,EAAE;4CACtC,UACE,gBAAgB,MAAM,EAAE,IACvB,MAAM,eAAe,IAAI,MAAM,mBAAmB,IAAI,MAAM,eAAe;4CAE9E,WAAU;sDAET,gBAAgB,MAAM,EAAE,GAAG,mBAC3B,MAAM,eAAe,IAAI,MAAM,mBAAmB,IAAI,MAAM,eAAe,GAAG,SAAS;;;;;;wCAG3F,MAAM,MAAM,KAAK,2BAChB,6LAAC;4CAAK,WAAU;sDAA+D;;;;;;wCAIhF,MAAM,MAAM,KAAK,6BAChB,6LAAC;4CAAK,WAAU;sDAA6D;;;;;;;;;;;;;;;;;;uBApF9E,MAAM,EAAE;;;;;;;;;;;;;;;;AAgG3B;GA3OwB;;QAOlB,+HAAA,CAAA,qBAAkB;;;KAPA", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/community/CommunityPosts.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  MessageSquare, \n  Heart, \n  Share2, \n  Pin, \n  Plus,\n  Filter,\n  User,\n  Calendar,\n  Tag,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react';\nimport { useCommunityPosts } from '@/hooks/useCommunity';\nimport { formatDate, formatTimeAgo } from '@/utils';\nimport { PostType, PostCategory } from '@/types';\n\nexport default function CommunityPosts() {\n  const {\n    posts,\n    loading,\n    error,\n    createPost,\n    clearError,\n  } = useCommunityPosts();\n\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [creating, setCreating] = useState(false);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [filter, setFilter] = useState<{\n    category?: PostCategory;\n    type?: PostType;\n  }>({});\n\n  const [newPost, setNewPost] = useState({\n    title: '',\n    content: '',\n    type: 'discussion' as PostType,\n    category: 'general' as PostCategory,\n    tags: '',\n  });\n\n  const handleCreatePost = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setCreating(true);\n\n    try {\n      await createPost({\n        ...newPost,\n        tags: newPost.tags.split(',').map(tag => tag.trim()).filter(Boolean),\n      });\n\n      setNewPost({\n        title: '',\n        content: '',\n        type: 'discussion',\n        category: 'general',\n        tags: '',\n      });\n      setShowCreateForm(false);\n      setSuccess('Post created successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to create post:', error);\n    } finally {\n      setCreating(false);\n    }\n  };\n\n  const getPostTypeIcon = (type: PostType) => {\n    switch (type) {\n      case 'question':\n        return '❓';\n      case 'announcement':\n        return '📢';\n      case 'tip':\n        return '💡';\n      case 'story':\n        return '📖';\n      case 'poll':\n        return '📊';\n      default:\n        return '💬';\n    }\n  };\n\n  const getPostTypeColor = (type: PostType) => {\n    switch (type) {\n      case 'question':\n        return 'text-blue-600 bg-blue-100';\n      case 'announcement':\n        return 'text-red-600 bg-red-100';\n      case 'tip':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'story':\n        return 'text-green-600 bg-green-100';\n      case 'poll':\n        return 'text-purple-600 bg-purple-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const filteredPosts = posts.filter(post => {\n    if (filter.category && post.category !== filter.category) return false;\n    if (filter.type && post.type !== filter.type) return false;\n    return true;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n          <MessageSquare className=\"w-7 h-7 mr-3 text-blue-600\" />\n          Community Posts\n        </h2>\n        <button\n          onClick={() => setShowCreateForm(!showCreateForm)}\n          className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow flex items-center\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          New Post\n        </button>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n          <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-red-700 text-sm\">{error}</p>\n          <button\n            onClick={clearError}\n            className=\"ml-auto text-red-600 hover:text-red-700\"\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\">\n          <CheckCircle className=\"w-5 h-5 text-green-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-green-700 text-sm\">{success}</p>\n        </div>\n      )}\n\n      {/* Create Post Form */}\n      {showCreateForm && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Create New Post</h3>\n          <form onSubmit={handleCreatePost} className=\"space-y-4\">\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Post Type\n                </label>\n                <select\n                  value={newPost.type}\n                  onChange={(e) => setNewPost(prev => ({ ...prev, type: e.target.value as PostType }))}\n                  className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"discussion\">Discussion</option>\n                  <option value=\"question\">Question</option>\n                  <option value=\"tip\">Tip</option>\n                  <option value=\"story\">Story</option>\n                  <option value=\"announcement\">Announcement</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Category\n                </label>\n                <select\n                  value={newPost.category}\n                  onChange={(e) => setNewPost(prev => ({ ...prev, category: e.target.value as PostCategory }))}\n                  className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"general\">General</option>\n                  <option value=\"investment\">Investment</option>\n                  <option value=\"property_management\">Property Management</option>\n                  <option value=\"market_trends\">Market Trends</option>\n                  <option value=\"legal\">Legal</option>\n                  <option value=\"technology\">Technology</option>\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Title\n              </label>\n              <input\n                type=\"text\"\n                value={newPost.title}\n                onChange={(e) => setNewPost(prev => ({ ...prev, title: e.target.value }))}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Enter post title\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Content\n              </label>\n              <textarea\n                value={newPost.content}\n                onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}\n                rows={4}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Write your post content...\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tags (comma-separated)\n              </label>\n              <input\n                type=\"text\"\n                value={newPost.tags}\n                onChange={(e) => setNewPost(prev => ({ ...prev, tags: e.target.value }))}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"e.g., investment, tips, beginner\"\n              />\n            </div>\n\n            <div className=\"flex space-x-3\">\n              <button\n                type=\"submit\"\n                disabled={creating}\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50\"\n              >\n                {creating ? 'Creating...' : 'Create Post'}\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowCreateForm(false)}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-semibold hover:bg-gray-400 transition-colors\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap gap-3\">\n        <select\n          value={filter.category || ''}\n          onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value as PostCategory || undefined }))}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"\">All Categories</option>\n          <option value=\"general\">General</option>\n          <option value=\"investment\">Investment</option>\n          <option value=\"property_management\">Property Management</option>\n          <option value=\"market_trends\">Market Trends</option>\n          <option value=\"legal\">Legal</option>\n          <option value=\"technology\">Technology</option>\n        </select>\n\n        <select\n          value={filter.type || ''}\n          onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value as PostType || undefined }))}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"\">All Types</option>\n          <option value=\"discussion\">Discussion</option>\n          <option value=\"question\">Question</option>\n          <option value=\"tip\">Tip</option>\n          <option value=\"story\">Story</option>\n          <option value=\"announcement\">Announcement</option>\n        </select>\n      </div>\n\n      {/* Posts List */}\n      {loading ? (\n        <div className=\"space-y-4\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 bg-gray-300 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-5 bg-gray-300 rounded w-2/3 mb-2\"></div>\n                  <div className=\"h-4 bg-gray-300 rounded w-full mb-2\"></div>\n                  <div className=\"h-4 bg-gray-300 rounded w-3/4\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : filteredPosts.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <MessageSquare className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Posts Found</h3>\n          <p className=\"text-gray-600\">Be the first to start a discussion!</p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {filteredPosts.map((post) => (\n            <div\n              key={post.id}\n              className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${\n                post.isSticky ? 'ring-2 ring-blue-200' : ''\n              }`}\n            >\n              <div className=\"flex items-start space-x-4\">\n                {/* Author Avatar */}\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <span className=\"text-white font-semibold text-sm\">\n                    {post.author?.name ? post.author.name.split(' ').map(n => n[0]).join('') : 'U'}\n                  </span>\n                </div>\n\n                <div className=\"flex-1\">\n                  {/* Post Header */}\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    {post.isSticky && <Pin className=\"w-4 h-4 text-blue-600\" />}\n                    <span className={`text-xs px-2 py-1 rounded-full ${getPostTypeColor(post.type)}`}>\n                      {getPostTypeIcon(post.type)} {post.type}\n                    </span>\n                    <span className=\"text-xs text-gray-500 capitalize\">\n                      {post.category.replace('_', ' ')}\n                    </span>\n                  </div>\n\n                  {/* Post Title */}\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{post.title}</h3>\n\n                  {/* Post Content */}\n                  <p className=\"text-gray-600 mb-3 line-clamp-3\">{post.content}</p>\n\n                  {/* Tags */}\n                  {post.tags.length > 0 && (\n                    <div className=\"flex flex-wrap gap-1 mb-3\">\n                      {post.tags.map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\"\n                        >\n                          #{tag}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n\n                  {/* Post Meta */}\n                  <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                    <div className=\"flex items-center space-x-4\">\n                      <span className=\"flex items-center\">\n                        <User className=\"w-4 h-4 mr-1\" />\n                        {post.author?.name || 'Anonymous'}\n                      </span>\n                      <span className=\"flex items-center\">\n                        <Calendar className=\"w-4 h-4 mr-1\" />\n                        {formatTimeAgo(post.createdAt)}\n                      </span>\n                    </div>\n\n                    <div className=\"flex items-center space-x-4\">\n                      <span className=\"flex items-center\">\n                        <Heart className=\"w-4 h-4 mr-1\" />\n                        {post.likes}\n                      </span>\n                      <span className=\"flex items-center\">\n                        <MessageSquare className=\"w-4 h-4 mr-1\" />\n                        {post.comments}\n                      </span>\n                      <span className=\"flex items-center\">\n                        <Share2 className=\"w-4 h-4 mr-1\" />\n                        {post.shares}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AAjBA;;;;;AAoBe,SAAS;;IACtB,MAAM,EACJ,KAAK,EACL,OAAO,EACP,KAAK,EACL,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGhC,CAAC;IAEJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM;IACR;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,YAAY;QAEZ,IAAI;YACF,MAAM,WAAW;gBACf,GAAG,OAAO;gBACV,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC;YAC9D;YAEA,WAAW;gBACT,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;YACA,kBAAkB;YAClB,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,IAAI,OAAO,QAAQ,IAAI,KAAK,QAAQ,KAAK,OAAO,QAAQ,EAAE,OAAO;QACjE,IAAI,OAAO,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,EAAE,OAAO;QACrD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAA+B;;;;;;;kCAG1D,6LAAC;wBACC,SAAS,IAAM,kBAAkB,CAAC;wBAClC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMJ,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;YAK1C,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAK,UAAU;wBAAkB,WAAU;;0CAC1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,QAAQ,IAAI;gDACnB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAa,CAAC;gDAClF,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAe;;;;;;;;;;;;;;;;;;kDAGjC,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAiB,CAAC;gDAC1F,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAsB;;;;;;kEACpC,6LAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,KAAK;wCACpB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,QAAQ,OAAO;wCACtB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACzE,MAAM;wCACN,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,IAAI;wCACnB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACtE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,WAAW,gBAAgB;;;;;;kDAE9B,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAO,OAAO,QAAQ,IAAI;wBAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAoB;gCAAU,CAAC;wBACtG,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,6LAAC;gCAAO,OAAM;0CAAsB;;;;;;0CACpC,6LAAC;gCAAO,OAAM;0CAAgB;;;;;;0CAC9B,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6LAAC;gCAAO,OAAM;0CAAa;;;;;;;;;;;;kCAG7B,6LAAC;wBACC,OAAO,OAAO,IAAI,IAAI;wBACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,EAAE,MAAM,CAAC,KAAK,IAAgB;gCAAU,CAAC;wBAC9F,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,6LAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,6LAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,6LAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6LAAC;gCAAO,OAAM;0CAAe;;;;;;;;;;;;;;;;;;YAKhC,wBACC,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;uBANX;;;;;;;;;uBAYZ,cAAc,MAAM,KAAK,kBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAG/B,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;wBAWT,cAyCI;yCAnDb,6LAAC;wBAEC,WAAW,AAAC,8FAEX,OADC,KAAK,QAAQ,GAAG,yBAAyB;kCAG3C,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,EAAA,eAAA,KAAK,MAAM,cAAX,mCAAA,aAAa,IAAI,IAAG,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;8CAI/E,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,QAAQ,kBAAI,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACjC,6LAAC;oDAAK,WAAW,AAAC,kCAA6D,OAA5B,iBAAiB,KAAK,IAAI;;wDAC1E,gBAAgB,KAAK,IAAI;wDAAE;wDAAE,KAAK,IAAI;;;;;;;8DAEzC,6LAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAKhC,6LAAC;4CAAG,WAAU;sDAA4C,KAAK,KAAK;;;;;;sDAGpE,6LAAC;4CAAE,WAAU;sDAAmC,KAAK,OAAO;;;;;;wCAG3D,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;oDAEC,WAAU;;wDACX;wDACG;;mDAHG;;;;;;;;;;sDAUb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,EAAA,gBAAA,KAAK,MAAM,cAAX,oCAAA,cAAa,IAAI,KAAI;;;;;;;sEAExB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;;8DAIjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,KAAK;;;;;;;sEAEb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEACxB,KAAK,QAAQ;;;;;;;sEAEhB,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBArEjB,KAAK,EAAE;;;;;;;;;;;;;;;;;AAiF1B;GAjXwB;;QAOlB,+HAAA,CAAA,oBAAiB;;;KAPC", "debugId": null}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/community/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Navigation from '@/components/layout/Navigation';\nimport {\n  ArrowLeft,\n  Building2,\n  Users,\n  Calendar,\n  MessageSquare,\n  TrendingUp,\n  Award,\n  Zap\n} from 'lucide-react';\nimport CommunityEvents from '@/components/community/CommunityEvents';\nimport CommunityPosts from '@/components/community/CommunityPosts';\nimport { useCommunityStats } from '@/hooks/useCommunity';\n\nexport default function CommunityPage() {\n  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'posts'>('overview');\n  const { stats, loading: statsLoading } = useCommunityStats();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Home\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold gradient-text\">ManageLife Community</span>\n              </div>\n            </div>\n            <Link\n              href=\"/auth/login\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n            >\n              Join Community\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n            Welcome to Our Community\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Connect with fellow real estate enthusiasts, share knowledge, \n            attend events, and grow together in the ManageLife ecosystem.\n          </p>\n          \n          {/* Community Stats */}\n          {!statsLoading && stats && (\n            <div className=\"grid md:grid-cols-4 gap-6 max-w-4xl mx-auto\">\n              <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                <Users className=\"w-8 h-8 mx-auto mb-2\" />\n                <p className=\"text-2xl font-bold\">{stats.activeMembers}</p>\n                <p className=\"text-sm text-blue-100\">Active Members</p>\n              </div>\n              <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                <Calendar className=\"w-8 h-8 mx-auto mb-2\" />\n                <p className=\"text-2xl font-bold\">{stats.totalEvents}</p>\n                <p className=\"text-sm text-blue-100\">Events Hosted</p>\n              </div>\n              <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                <MessageSquare className=\"w-8 h-8 mx-auto mb-2\" />\n                <p className=\"text-2xl font-bold\">{stats.totalPosts}</p>\n                <p className=\"text-sm text-blue-100\">Community Posts</p>\n              </div>\n              <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                <TrendingUp className=\"w-8 h-8 mx-auto mb-2\" />\n                <p className=\"text-2xl font-bold\">{stats.totalParticipants}</p>\n                <p className=\"text-sm text-blue-100\">Event Participants</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Features Overview */}\n      <section className=\"py-12 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Community Features\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Discover all the ways you can engage with our vibrant community\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Calendar className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Events & Workshops</h3>\n              <p className=\"text-gray-600\">\n                Join webinars, workshops, and networking events to learn from experts and connect with peers.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <MessageSquare className=\"w-8 h-8 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Discussion Forums</h3>\n              <p className=\"text-gray-600\">\n                Share insights, ask questions, and participate in meaningful discussions about real estate.\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Award className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Rewards & Recognition</h3>\n              <p className=\"text-gray-600\">\n                Earn $MLIFE tokens for active participation and unlock exclusive community benefits.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Main Content */}\n      <section className=\"py-12\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Tab Navigation */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-gray-100 rounded-lg p-1 flex\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'overview'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <TrendingUp className=\"w-4 h-4 inline mr-2\" />\n                Overview\n              </button>\n              <button\n                onClick={() => setActiveTab('events')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'events'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <Calendar className=\"w-4 h-4 inline mr-2\" />\n                Events\n              </button>\n              <button\n                onClick={() => setActiveTab('posts')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'posts'\n                    ? 'bg-white text-blue-600 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <MessageSquare className=\"w-4 h-4 inline mr-2\" />\n                Discussions\n              </button>\n            </div>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"max-w-6xl mx-auto\">\n            {activeTab === 'overview' && (\n              <div className=\"space-y-8\">\n                {/* Welcome Section */}\n                <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center\">\n                  <Zap className=\"w-16 h-16 text-blue-600 mx-auto mb-4\" />\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                    Join Our Growing Community\n                  </h3>\n                  <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n                    Connect with real estate professionals, investors, and enthusiasts. \n                    Share knowledge, learn from experts, and grow your network in our supportive community.\n                  </p>\n                  <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                    <Link\n                      href=\"/auth/register\"\n                      className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n                    >\n                      Join Community\n                    </Link>\n                    <button\n                      onClick={() => setActiveTab('events')}\n                      className=\"border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors\"\n                    >\n                      Browse Events\n                    </button>\n                  </div>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid md:grid-cols-2 gap-8\">\n                  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h4>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <Calendar className=\"w-4 h-4 mr-2 text-blue-600\" />\n                        <span>{stats?.upcomingEvents || 0} upcoming events this month</span>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <MessageSquare className=\"w-4 h-4 mr-2 text-purple-600\" />\n                        <span>{stats?.totalPosts || 0} community discussions</span>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <Users className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span>{stats?.activeMembers || 0} active community members</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Community Benefits</h4>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <Award className=\"w-4 h-4 mr-2 text-yellow-600\" />\n                        <span>Earn $MLIFE tokens for participation</span>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <Zap className=\"w-4 h-4 mr-2 text-blue-600\" />\n                        <span>Access to exclusive events and content</span>\n                      </div>\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <TrendingUp className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span>Network with industry professionals</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'events' && <CommunityEvents />}\n            {activeTab === 'posts' && <CommunityPosts />}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAC5E,MAAM,EAAE,KAAK,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;0CAGtD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA+C;;;;;;wBAM3D,CAAC,gBAAgB,uBAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,WAAW;;;;;;sDACpD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,UAAU;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,iBAAiB;;;;;;sDAC1D,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,sDAIX,OAHC,cAAc,aACV,qCACA;;0DAGN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGhD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,sDAIX,OAHC,cAAc,WACV,qCACA;;0DAGN,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG9C,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,sDAIX,OAHC,cAAc,UACV,qCACA;;0DAGN,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;sCAOvD,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,4BACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAGtD,6LAAC;oDAAE,WAAU;8DAAuC;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAOL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;;gFAAM,CAAA,kBAAA,4BAAA,MAAO,cAAc,KAAI;gFAAE;;;;;;;;;;;;;8EAEpC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,2NAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;sFACzB,6LAAC;;gFAAM,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;gFAAE;;;;;;;;;;;;;8EAEhC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;;gFAAM,CAAA,kBAAA,4BAAA,MAAO,aAAa,KAAI;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;8DAKvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;sFAAK;;;;;;;;;;;;8EAER,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,6LAAC;sFAAK;;;;;;;;;;;;8EAER,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,6LAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQjB,cAAc,0BAAY,6LAAC,qJAAA,CAAA,UAAe;;;;;gCAC1C,cAAc,yBAAW,6LAAC,oJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD;GA9OwB;;QAEmB,+HAAA,CAAA,oBAAiB;;;KAFpC", "debugId": null}}]}