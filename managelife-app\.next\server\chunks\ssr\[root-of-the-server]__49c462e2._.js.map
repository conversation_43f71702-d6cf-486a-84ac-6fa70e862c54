{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/support/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { \n  ArrowLeft, \n  Building2, \n  MessageCircle,\n  Mail,\n  Phone,\n  Clock,\n  Send,\n  CheckCircle,\n  AlertCircle,\n  HelpCircle,\n  Book,\n  Users\n} from 'lucide-react';\n\nexport default function SupportPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    category: 'general',\n    message: '',\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate form submission\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setIsSubmitted(true);\n    }, 2000);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Home\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n              </div>\n            </div>\n            <Link\n              href=\"/auth/register\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n            >\n              Get Started\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <MessageCircle className=\"w-16 h-16 mx-auto mb-6\" />\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            Support Center\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            We're here to help! Get support, find answers, and connect with our team.\n          </p>\n        </div>\n      </section>\n\n      {/* Quick Help Options */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">How Can We Help?</h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Choose the best way to get the support you need\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <Link href=\"/docs\" className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors\">\n                <Book className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Documentation</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Find detailed guides, tutorials, and API documentation to help you get the most out of ManageLife.\n              </p>\n              <span className=\"text-blue-600 font-medium group-hover:text-blue-700\">\n                Browse Docs →\n              </span>\n            </Link>\n\n            <Link href=\"/community\" className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors\">\n                <Users className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Community</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Connect with other users, ask questions, and share experiences in our active community.\n              </p>\n              <span className=\"text-blue-600 font-medium group-hover:text-blue-700\">\n                Join Community →\n              </span>\n            </Link>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                <HelpCircle className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Direct Support</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Need personalized help? Contact our support team directly using the form below.\n              </p>\n              <span className=\"text-blue-600 font-medium\">\n                Contact Us ↓\n              </span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Information */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Mail className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Email Support</h3>\n              <p className=\"text-gray-600 mb-2\">Get help via email</p>\n              <a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:text-blue-700 font-medium\">\n                <EMAIL>\n              </a>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <MessageCircle className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Live Chat</h3>\n              <p className=\"text-gray-600 mb-2\">Chat with our team</p>\n              <button className=\"text-blue-600 hover:text-blue-700 font-medium\">\n                Start Chat\n              </button>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Clock className=\"w-8 h-8 text-purple-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Response Time</h3>\n              <p className=\"text-gray-600 mb-2\">We typically respond within</p>\n              <span className=\"text-blue-600 font-medium\">24 hours</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-2xl mx-auto\">\n            <div className=\"text-center mb-8\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Send Us a Message</h2>\n              <p className=\"text-xl text-gray-600\">\n                Fill out the form below and we'll get back to you as soon as possible.\n              </p>\n            </div>\n\n            {isSubmitted ? (\n              <div className=\"bg-green-50 border border-green-200 rounded-xl p-8 text-center\">\n                <CheckCircle className=\"w-16 h-16 text-green-600 mx-auto mb-4\" />\n                <h3 className=\"text-2xl font-bold text-green-900 mb-4\">Message Sent!</h3>\n                <p className=\"text-green-700 mb-6\">\n                  Thank you for contacting us. We've received your message and will respond within 24 hours.\n                </p>\n                <button\n                  onClick={() => {\n                    setIsSubmitted(false);\n                    setFormData({\n                      name: '',\n                      email: '',\n                      subject: '',\n                      category: 'general',\n                      message: '',\n                    });\n                  }}\n                  className=\"bg-green-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n                >\n                  Send Another Message\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-8\">\n                <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Name *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"Your full name\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Email *\n                    </label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Category\n                    </label>\n                    <select\n                      name=\"category\"\n                      value={formData.category}\n                      onChange={handleInputChange}\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"general\">General Question</option>\n                      <option value=\"technical\">Technical Issue</option>\n                      <option value=\"billing\">Billing & Payments</option>\n                      <option value=\"property\">Property Management</option>\n                      <option value=\"blockchain\">Blockchain & Tokens</option>\n                      <option value=\"account\">Account Issues</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Subject *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      placeholder=\"Brief description of your issue\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mb-6\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Message *\n                  </label>\n                  <textarea\n                    name=\"message\"\n                    value={formData.message}\n                    onChange={handleInputChange}\n                    required\n                    rows={6}\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Please provide as much detail as possible about your question or issue...\"\n                  />\n                </div>\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                      Sending...\n                    </>\n                  ) : (\n                    <>\n                      <Send className=\"w-5 h-5 mr-2\" />\n                      Send Message\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 text-center\">Common Questions</h2>\n            \n            <div className=\"space-y-4\">\n              <details className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                <summary className=\"p-6 cursor-pointer font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\n                  How do I reset my password?\n                </summary>\n                <div className=\"px-6 pb-6 text-gray-600\">\n                  You can reset your password by clicking the \"Forgot Password\" link on the login page. \n                  We'll send you an email with instructions to create a new password.\n                </div>\n              </details>\n\n              <details className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                <summary className=\"p-6 cursor-pointer font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\n                  How do I connect my MetaMask wallet?\n                </summary>\n                <div className=\"px-6 pb-6 text-gray-600\">\n                  Go to your dashboard and click on \"Connect Wallet\" or visit the Web3 features page. \n                  Make sure you have MetaMask installed and follow the prompts to connect your wallet.\n                </div>\n              </details>\n\n              <details className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                <summary className=\"p-6 cursor-pointer font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\n                  Where can I see my $MLIFE token balance?\n                </summary>\n                <div className=\"px-6 pb-6 text-gray-600\">\n                  Your $MLIFE balance is displayed in your dashboard. You can also view detailed \n                  transaction history and claim pending rewards in the Rewards section.\n                </div>\n              </details>\n\n              <details className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n                <summary className=\"p-6 cursor-pointer font-semibold text-gray-900 hover:text-blue-600 transition-colors\">\n                  How do I tokenize my property?\n                </summary>\n                <div className=\"px-6 pb-6 text-gray-600\">\n                  Visit the Web3 features page and use the Property Tokenizer tool. You'll need to \n                  connect your wallet and provide property details to create an NFT representation.\n                </div>\n              </details>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,WAAW;YACT,gBAAgB;YAChB,eAAe;QACjB,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;0CAGtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;8CAKxE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAK,WAAU;sDAAsD;;;;;;;;;;;;8CAKxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8NAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAE,MAAK;wCAAgC,WAAU;kDAAgD;;;;;;;;;;;;0CAKpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAO,WAAU;kDAAgD;;;;;;;;;;;;0CAKpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;4BAKtC,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDAGnC,8OAAC;wCACC,SAAS;4CACP,eAAe;4CACf,YAAY;gDACV,MAAM;gDACN,OAAO;gDACP,SAAS;gDACT,UAAU;gDACV,SAAS;4CACX;wCACF;wCACA,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAG5B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAA0F;;yEAI3G;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAElE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAQ,WAAU;0DAAuF;;;;;;0DAG1G,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;kDAM3C,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAQ,WAAU;0DAAuF;;;;;;0DAG1G,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;kDAM3C,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAQ,WAAU;0DAAuF;;;;;;0DAG1G,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;kDAM3C,8OAAC;wCAAQ,WAAU;;0DACjB,8OAAC;gDAAQ,WAAU;0DAAuF;;;;;;0DAG1G,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}]}