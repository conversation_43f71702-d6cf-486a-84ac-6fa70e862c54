'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import Navigation from '@/components/layout/Navigation';
import {
  Building2,
  ArrowRight,
  Shield,
  DollarSign,
  Users,
  Zap,
  CheckCircle,
  AlertTriangle,
  FileText,
  Clock,
  ExternalLink
} from 'lucide-react';

export default function TokenizePage() {
  const router = useRouter();
  const { user } = useAuth();

  const handleStartTokenization = () => {
    if (!user) {
      router.push('/auth/login?redirect=/tokenize');
      return;
    }
    router.push('/tokenize/llc-setup');
  };

  const steps = [
    {
      number: 1,
      title: 'Form Your LLC',
      description: 'Create a Limited Liability Company to legally own your property',
      icon: Building2,
      color: 'blue',
      estimated: '3-5 days'
    },
    {
      number: 2,
      title: 'Transfer Property',
      description: 'Transfer property ownership from personal name to your LLC',
      icon: FileText,
      color: 'green',
      estimated: '1-2 weeks'
    },
    {
      number: 3,
      title: 'Property Verification',
      description: 'Verify property details, ownership, and compliance requirements',
      icon: Shield,
      color: 'purple',
      estimated: '2-3 days'
    },
    {
      number: 4,
      title: 'Create NFT',
      description: 'Mint your property as an NFT on the blockchain',
      icon: Zap,
      color: 'orange',
      estimated: '1 day'
    },
    {
      number: 5,
      title: 'List & Trade',
      description: 'List your tokenized property on the marketplace',
      icon: Users,
      color: 'indigo',
      estimated: 'Immediate'
    }
  ];

  const benefits = [
    {
      icon: Shield,
      title: 'Legal Compliance',
      description: 'Full regulatory compliance through proper LLC structure'
    },
    {
      icon: DollarSign,
      title: 'Fractional Ownership',
      description: 'Enable multiple investors to own shares of your property'
    },
    {
      icon: Users,
      title: 'Liquidity',
      description: 'Trade property shares instantly on the blockchain'
    },
    {
      icon: Zap,
      title: 'Transparency',
      description: 'All transactions recorded immutably on the blockchain'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Tokenize Your Real Estate
            </h1>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Transform your property into a digital asset. Create an LLC, transfer ownership, 
              and mint your property as an NFT for fractional investment opportunities.
            </p>
            <button
              onClick={handleStartTokenization}
              className="bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors flex items-center mx-auto"
            >
              Start Tokenization Process
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Why Tokenize Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Tokenize Your Property?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            Property tokenization unlocks new opportunities for real estate investment, 
            providing liquidity, transparency, and accessibility like never before.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Process Steps */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Tokenization Process</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Follow our step-by-step process to legally and securely tokenize your real estate property.
            </p>
          </div>

          <div className="space-y-8">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isLast = index === steps.length - 1;
              
              return (
                <div key={step.number} className="relative">
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 bg-${step.color}-100 rounded-full flex items-center justify-center`}>
                        <Icon className={`w-6 h-6 text-${step.color}-600`} />
                      </div>
                    </div>
                    <div className="flex-1 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-xl font-semibold text-gray-900">
                          Step {step.number}: {step.title}
                        </h3>
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="w-4 h-4 mr-1" />
                          {step.estimated}
                        </div>
                      </div>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                  
                  {!isLast && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Requirements Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Requirements & Prerequisites</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                What You Need
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                  Property ownership or purchase agreement
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                  Property appraisal or market valuation
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                  Valid government-issued ID
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                  Business address for LLC registration
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                  Cryptocurrency wallet (MetaMask recommended)
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
                Important Considerations
              </h3>
              <ul className="space-y-3 text-gray-600">
                <li className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                  LLC formation costs vary by state ($75-$500)
                </li>
                <li className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                  Property transfer may trigger tax implications
                </li>
                <li className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                  Existing mortgages may need lender approval
                </li>
                <li className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                  Compliance with local real estate regulations
                </li>
                <li className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                  Blockchain transaction fees apply
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl text-white p-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join the future of real estate investment. Our guided process makes tokenization 
              simple, legal, and secure.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleStartTokenization}
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center"
              >
                Begin LLC Formation
                <ArrowRight className="w-4 h-4 ml-2" />
              </button>
              <button
                onClick={() => router.push('/docs')}
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center"
              >
                Learn More
                <ExternalLink className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
