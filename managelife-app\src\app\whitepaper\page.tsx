import Link from 'next/link';
import { 
  ArrowLeft, 
  Building2, 
  Download,
  FileText,
  ExternalLink,
  Coins,
  Shield,
  TrendingUp,
  Users,
  Zap,
  Globe
} from 'lucide-react';

export default function WhitepaperPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Link>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold gradient-text">ManageLife</span>
              </div>
            </div>
            <Link
              href="/auth/register"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <FileText className="w-16 h-16 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            ManageLife Whitepaper
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            A comprehensive technical overview of the ManageLife platform, 
            tokenomics, and the future of real estate tokenization.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <span className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center cursor-pointer">
              <Download className="w-5 h-5 mr-2" />
              Download PDF
            </span>
            <span className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center cursor-pointer">
              <ExternalLink className="w-5 h-5 mr-2" />
              View Online
            </span>
          </div>
        </div>
      </section>

      {/* Table of Contents */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Table of Contents</h2>
            
            <div className="bg-gray-50 rounded-xl p-8">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900 mb-4">Part I: Foundation</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex justify-between">
                      <span>1. Executive Summary</span>
                      <span>Page 3</span>
                    </div>
                    <div className="flex justify-between">
                      <span>2. Problem Statement</span>
                      <span>Page 5</span>
                    </div>
                    <div className="flex justify-between">
                      <span>3. Solution Overview</span>
                      <span>Page 8</span>
                    </div>
                    <div className="flex justify-between">
                      <span>4. Market Analysis</span>
                      <span>Page 12</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900 mb-4">Part II: Technology</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex justify-between">
                      <span>5. Platform Architecture</span>
                      <span>Page 16</span>
                    </div>
                    <div className="flex justify-between">
                      <span>6. Blockchain Integration</span>
                      <span>Page 20</span>
                    </div>
                    <div className="flex justify-between">
                      <span>7. Smart Contracts</span>
                      <span>Page 24</span>
                    </div>
                    <div className="flex justify-between">
                      <span>8. Security Framework</span>
                      <span>Page 28</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900 mb-4">Part III: Tokenomics</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex justify-between">
                      <span>9. $MLIFE Token</span>
                      <span>Page 32</span>
                    </div>
                    <div className="flex justify-between">
                      <span>10. NFTi & NFTr System</span>
                      <span>Page 36</span>
                    </div>
                    <div className="flex justify-between">
                      <span>11. Reward Mechanisms</span>
                      <span>Page 40</span>
                    </div>
                    <div className="flex justify-between">
                      <span>12. Economic Model</span>
                      <span>Page 44</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-gray-900 mb-4">Part IV: Implementation</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex justify-between">
                      <span>13. Roadmap</span>
                      <span>Page 48</span>
                    </div>
                    <div className="flex justify-between">
                      <span>14. Team & Advisors</span>
                      <span>Page 52</span>
                    </div>
                    <div className="flex justify-between">
                      <span>15. Legal Framework</span>
                      <span>Page 56</span>
                    </div>
                    <div className="flex justify-between">
                      <span>16. Risk Assessment</span>
                      <span>Page 60</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Highlights */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Key Highlights</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Essential insights from the ManageLife whitepaper
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Coins className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Dual Token System</h3>
              <p className="text-gray-600">
                Innovative NFTi (property ownership) and NFTr (rental rights) tokens 
                that separate ownership from usage rights, creating new investment opportunities.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">$MLIFE Economy</h3>
              <p className="text-gray-600">
                Comprehensive reward system that incentivizes positive behavior 
                across all platform participants, creating a sustainable token economy.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Security First</h3>
              <p className="text-gray-600">
                Multi-layered security architecture with smart contract audits, 
                formal verification, and comprehensive risk management protocols.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Multi-Role Platform</h3>
              <p className="text-gray-600">
                Designed for five distinct user roles with tailored features 
                and incentives for homeowners, renters, buyers, portfolio managers, and community members.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Instant Settlements</h3>
              <p className="text-gray-600">
                Smart contract automation enables instant property transfers, 
                rent payments, and reward distributions without intermediaries.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <Globe className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Global Accessibility</h3>
              <p className="text-gray-600">
                Cross-border real estate investment made possible through 
                blockchain technology and regulatory compliance frameworks.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Executive Summary Preview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Executive Summary</h2>
            
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-600 leading-relaxed mb-6">
                ManageLife represents a paradigm shift in real estate investment and management, 
                leveraging blockchain technology to create a transparent, efficient, and accessible 
                ecosystem for property stakeholders worldwide.
              </p>
              
              <p className="text-gray-600 leading-relaxed mb-6">
                Our platform addresses critical inefficiencies in traditional real estate markets 
                through innovative tokenization mechanisms, automated smart contracts, and a 
                comprehensive reward system that aligns incentives across all participants.
              </p>
              
              <p className="text-gray-600 leading-relaxed mb-6">
                The dual token architecture (NFTi for ownership, NFTr for rental rights) enables 
                unprecedented flexibility in property investment strategies, while the $MLIFE token 
                creates a sustainable economy that rewards positive behavior and platform engagement.
              </p>
              
              <div className="bg-blue-50 border-l-4 border-blue-500 p-6 my-8">
                <p className="text-blue-800 font-medium">
                  "ManageLife is not just a platform; it's a complete reimagining of how real estate 
                  can work in the digital age, making property investment accessible to everyone while 
                  maintaining the highest standards of security and transparency."
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Download CTA */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Dive Deeper?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Download the complete whitepaper to explore the technical details, 
            tokenomics, and roadmap for the future of real estate.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <span className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center cursor-pointer">
              <Download className="w-5 h-5 mr-2" />
              Download Full Whitepaper
            </span>
            <Link
              href="/docs"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center"
            >
              Explore Documentation
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
