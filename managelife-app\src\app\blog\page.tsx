import Link from 'next/link';
import { 
  ArrowLeft, 
  Building2, 
  Calendar,
  User,
  Clock,
  ArrowRight,
  TrendingUp,
  Coins,
  Shield,
  Zap
} from 'lucide-react';

// Mock blog posts data
const blogPosts = [
  {
    id: '1',
    title: 'The Future of Real Estate: How Blockchain is Revolutionizing Property Investment',
    excerpt: 'Discover how blockchain technology is transforming the real estate industry, making property investment more accessible and transparent than ever before.',
    content: 'Full article content would go here...',
    author: '<PERSON>',
    author<PERSON><PERSON>: 'Head of Research',
    publishedAt: '2024-01-15',
    readTime: '8 min read',
    category: 'Technology',
    image: '/api/placeholder/600/300',
    featured: true,
  },
  {
    id: '2',
    title: 'Understanding NFTi and NFTr: The Dual Token System Explained',
    excerpt: 'Learn about our innovative dual token system that separates property ownership (NFTi) from rental rights (NFTr), creating new opportunities for investors.',
    content: 'Full article content would go here...',
    author: '<PERSON>',
    authorRole: 'Blockchain Developer',
    publishedAt: '2024-01-12',
    readTime: '6 min read',
    category: 'Education',
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: '3',
    title: 'Maximizing Your $MLIFE Rewards: Tips and Strategies',
    excerpt: 'Get the most out of the ManageLife platform with these proven strategies for earning and maximizing your $MLIFE token rewards.',
    content: 'Full article content would go here...',
    author: 'Emily Rodriguez',
    authorRole: 'Community Manager',
    publishedAt: '2024-01-10',
    readTime: '5 min read',
    category: 'Tips',
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: '4',
    title: 'Market Analysis: Real Estate Tokenization Trends in 2024',
    excerpt: 'An in-depth analysis of current market trends in real estate tokenization and what to expect in the coming year.',
    content: 'Full article content would go here...',
    author: 'David Park',
    authorRole: 'Market Analyst',
    publishedAt: '2024-01-08',
    readTime: '10 min read',
    category: 'Market Analysis',
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: '5',
    title: 'Getting Started: Your First Property Tokenization',
    excerpt: 'A step-by-step guide for property owners looking to tokenize their first property on the ManageLife platform.',
    content: 'Full article content would go here...',
    author: 'Lisa Wang',
    authorRole: 'Product Manager',
    publishedAt: '2024-01-05',
    readTime: '7 min read',
    category: 'Tutorial',
    image: '/api/placeholder/600/300',
    featured: false,
  },
  {
    id: '6',
    title: 'Community Spotlight: Success Stories from ManageLife Users',
    excerpt: 'Read inspiring stories from our community members who have successfully leveraged the ManageLife platform for their real estate goals.',
    content: 'Full article content would go here...',
    author: 'Alex Thompson',
    authorRole: 'Content Writer',
    publishedAt: '2024-01-03',
    readTime: '4 min read',
    category: 'Community',
    image: '/api/placeholder/600/300',
    featured: false,
  },
];

const categories = ['All', 'Technology', 'Education', 'Tips', 'Market Analysis', 'Tutorial', 'Community'];

export default function BlogPage() {
  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Link>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold gradient-text">ManageLife</span>
              </div>
            </div>
            <Link
              href="/auth/register"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow"
            >
              Get Started
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            ManageLife Blog
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Insights, tutorials, and updates from the world of real estate tokenization and blockchain technology.
          </p>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <img
                      src={featuredPost.image}
                      alt={featuredPost.title}
                      className="w-full h-64 md:h-full object-cover"
                    />
                  </div>
                  <div className="md:w-1/2 p-8">
                    <div className="flex items-center space-x-2 mb-4">
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        Featured
                      </span>
                      <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        {featuredPost.category}
                      </span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">
                      {featuredPost.title}
                    </h2>
                    <p className="text-gray-600 mb-6">
                      {featuredPost.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {featuredPost.author}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(featuredPost.publishedAt).toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {featuredPost.readTime}
                        </div>
                      </div>
                      <Link
                        href={`/blog/${featuredPost.id}`}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
                      >
                        Read More
                        <ArrowRight className="w-4 h-4 ml-1" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Categories Filter */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <span
                key={category}
                className="px-4 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-colors cursor-pointer"
              >
                {category}
              </span>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      {post.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      {post.author}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(post.publishedAt).toLocaleDateString()}
                    </span>
                    <Link
                      href={`/blog/${post.id}`}
                      className="text-blue-600 hover:text-blue-700 font-medium flex items-center"
                    >
                      Read More
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Stay Updated
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Subscribe to our newsletter for the latest insights on real estate tokenization and blockchain technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow cursor-pointer inline-block">
                Subscribe
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Related Topics */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Explore Topics</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Dive deeper into the topics that matter most in real estate tokenization
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Market Trends</h3>
              <p className="text-gray-600 text-sm">Latest trends and analysis in real estate tokenization</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Coins className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Token Economy</h3>
              <p className="text-gray-600 text-sm">Understanding $MLIFE and the platform economy</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Security</h3>
              <p className="text-gray-600 text-sm">Blockchain security and best practices</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Innovation</h3>
              <p className="text-gray-600 text-sm">Latest innovations in PropTech and blockchain</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
