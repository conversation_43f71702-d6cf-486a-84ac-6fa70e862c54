import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

const JWT_SECRET = process.env.JWT_SECRET || 'managelife-secret-key-2025';
const DB_PATH = path.join(process.cwd(), 'data');
const USERS_FILE = path.join(DB_PATH, 'users.json');
const PREFERENCES_FILE = path.join(DB_PATH, 'preferences.json');

// Ensure data directory exists
if (!fs.existsSync(DB_PATH)) {
  fs.mkdirSync(DB_PATH, { recursive: true });
}

// Initialize preferences file if it doesn't exist
if (!fs.existsSync(PREFERENCES_FILE)) {
  fs.writeFileSync(PREFERENCES_FILE, JSON.stringify({}, null, 2));
}

function readUsers() {
  try {
    const data = fs.readFileSync(USERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

function writeUsers(users: any[]) {
  fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
}

function readPreferences() {
  try {
    const data = fs.readFileSync(PREFERENCES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return {};
  }
}

function writePreferences(preferences: any) {
  fs.writeFileSync(PREFERENCES_FILE, JSON.stringify(preferences, null, 2));
}

export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const users = readUsers();
    const preferences = readPreferences();

    // Get user settings
    const user = users.find((u: any) => u.id === decoded.userId);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user preferences (if exists)
    const userPrefs = preferences[decoded.userId] || {};

    const settings = {
      // Profile settings
      displayName: user.name || '',
      bio: user.bio || '',
      location: user.location || '',
      website: user.website || '',

      // Privacy settings
      profileVisibility: userPrefs.profileVisibility || 'public',
      showEmail: userPrefs.showEmail === true,
      showWallet: userPrefs.showWallet === true,
      showActivity: userPrefs.showActivity !== false, // default true

      // Notification settings
      emailNotifications: userPrefs.emailNotifications !== false, // default true
      pushNotifications: userPrefs.pushNotifications !== false, // default true
      rewardNotifications: userPrefs.rewardNotifications !== false, // default true
      communityNotifications: userPrefs.communityNotifications === true,
      marketingEmails: userPrefs.marketingEmails === true,

      // App preferences
      language: userPrefs.language || 'en',
      currency: userPrefs.currency || 'usd',
      timezone: userPrefs.timezone || 'UTC',
      theme: userPrefs.theme || 'light',

      // Security settings
      twoFactorEnabled: userPrefs.twoFactorEnabled === true,
      loginAlerts: userPrefs.loginAlerts !== false, // default true
      sessionTimeout: userPrefs.sessionTimeout || 30,
    };

    return NextResponse.json({ settings });
  } catch (error) {
    console.error('Settings fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const body = await request.json();
    const users = readUsers();
    const preferences = readPreferences();

    // Find user
    const userIndex = users.findIndex((u: any) => u.id === decoded.userId);
    if (userIndex === -1) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update user profile information
    if (body.displayName !== undefined) users[userIndex].name = body.displayName;
    if (body.bio !== undefined) users[userIndex].bio = body.bio;
    if (body.location !== undefined) users[userIndex].location = body.location;
    if (body.website !== undefined) users[userIndex].website = body.website;

    // Save updated user data
    writeUsers(users);

    // Update user preferences
    preferences[decoded.userId] = {
      ...preferences[decoded.userId],
      profileVisibility: body.profileVisibility || 'public',
      showEmail: body.showEmail === true,
      showWallet: body.showWallet === true,
      showActivity: body.showActivity !== false,
      emailNotifications: body.emailNotifications !== false,
      pushNotifications: body.pushNotifications !== false,
      rewardNotifications: body.rewardNotifications !== false,
      communityNotifications: body.communityNotifications === true,
      marketingEmails: body.marketingEmails === true,
      language: body.language || 'en',
      currency: body.currency || 'usd',
      timezone: body.timezone || 'UTC',
      theme: body.theme || 'light',
      twoFactorEnabled: body.twoFactorEnabled === true,
      loginAlerts: body.loginAlerts !== false,
      sessionTimeout: body.sessionTimeout || 30,
      updatedAt: new Date().toISOString(),
    };

    // Save updated preferences
    writePreferences(preferences);

    return NextResponse.json({
      message: 'Settings updated successfully',
      success: true
    });
  } catch (error) {
    console.error('Settings update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const body = await request.json();
    const users = readUsers();

    // Handle password change
    if (body.action === 'change_password') {
      const { currentPassword, newPassword } = body;

      if (!currentPassword || !newPassword) {
        return NextResponse.json({ error: 'Current and new passwords are required' }, { status: 400 });
      }

      // Get current user
      const userIndex = users.findIndex((u: any) => u.id === decoded.userId);

      if (userIndex === -1) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      const user = users[userIndex];

      if (!user.password) {
        return NextResponse.json({ error: 'No password set for this account' }, { status: 400 });
      }

      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);

      if (!isValidPassword) {
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 });
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      users[userIndex].password = hashedNewPassword;
      writeUsers(users);

      return NextResponse.json({
        message: 'Password updated successfully',
        success: true
      });
    }

    // Handle account deletion
    if (body.action === 'delete_account') {
      // In a real app, you might want to:
      // 1. Transfer NFTs to a recovery wallet
      // 2. Archive data instead of deleting
      // 3. Send confirmation email
      // 4. Add a grace period

      // For now, just mark account as deleted
      const userIndex = users.findIndex((u: any) => u.id === decoded.userId);

      if (userIndex === -1) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      users[userIndex].deletedAt = new Date().toISOString();
      writeUsers(users);

      return NextResponse.json({
        message: 'Account deletion initiated. You will receive a confirmation email.',
        success: true
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Settings action error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
