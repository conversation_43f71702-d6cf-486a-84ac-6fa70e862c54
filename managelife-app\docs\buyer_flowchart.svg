<svg width="600" height="800" viewBox="0 0 600 800" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node { fill: lightblue; stroke: black; stroke-width: 1; }
    .start-end { fill: lightgreen; }
    .decision { fill: yellow; }
    .text { font: 12px sans-serif; text-anchor: middle; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
    .title { font: 16px sans-serif; text-anchor: middle; font-weight: bold; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>
  <!-- Role Title -->
  <text x="300" y="20" class="title">Buyer Role</text>
  <!-- Start -->
  <ellipse cx="300" cy="50" rx="80" ry="30" class="start-end node" />
  <text x="300" y="55" class="text">Start: Login/Register</text>
  <!-- Arrow to Browse Properties -->
  <line x1="300" y1="80" x2="300" y2="130" class="arrow" />
  <!-- Browse Properties -->
  <rect x="220" y="130" width="160" height="40" class="node" />
  <text x="300" y="150" class="text">Browse Properties</text>
  <!-- Arrow to Purchase Tokens -->
  <line x1="300" y1="170" x2="300" y2="220" class="arrow" />
  <!-- Purchase Tokens -->
  <rect x="220" y="220" width="160" height="40" class="node" />
  <text x="300" y="240" class="text">Purchase Tokens (NFTr/NFTi)</text>
  <!-- Arrow to Track Investment -->
  <line x1="300" y1="260" x2="300" y2="310" class="arrow" />
  <!-- Track Investment -->
  <rect x="220" y="310" width="160" height="40" class="node" />
  <text x="300" y="330" class="text">Track Investment Performance</text>
  <!-- Arrow to Decision: Sell? -->
  <line x1="300" y1="350" x2="300" y2="400" class="arrow" />
  <!-- Decision: Sell? -->
  <polygon points="300,400 360,430 300,460 240,430" class="decision node" />
  <text x="300" y="435" class="text">Sell Tokens?</text>
  <!-- Arrow Yes to Sell Tokens -->
  <line x1="360" y1="430" x2="450" y2="430" class="arrow" />
  <text x="405" y="420" class="text">Yes</text>
  <line x1="450" y1="430" x2="450" y2="480" class="arrow" />
  <!-- Sell Tokens -->
  <rect x="370" y="480" width="160" height="40" class="node" />
  <text x="450" y="500" class="text">Sell Tokens on Marketplace</text>
  <!-- Arrow to End -->
  <line x1="450" y1="520" x2="450" y2="570" class="arrow" />
  <!-- End -->
  <ellipse cx="450" cy="600" rx="80" ry="30" class="start-end node" />
  <text x="450" y="605" class="text">End</text>
  <!-- Arrow No back to Track -->
  <line x1="240" y1="430" x2="150" y2="430" class="arrow" />
  <text x="195" y="420" class="text">No</text>
  <line x1="150" y1="430" x2="150" y2="330" class="arrow" />
  <line x1="150" y1="330" x2="220" y2="330" class="arrow" />
</svg>