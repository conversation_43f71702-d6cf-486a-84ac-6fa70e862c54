import React, { useState, useEffect } from 'react';
import { Users, Gift, MessageSquare, BarChart2 } from 'lucide-react';

import { useAuth } from '@/contexts/AuthContext';
import { useMLifeToken } from '@/hooks/useWeb3';

export default function CommunityMemberDashboard() {
  const { user } = useAuth();
  const { balanceOf, claimRewards } = useMLifeToken();
  const [earnedRewards, setEarnedRewards] = useState(0);

  useEffect(() => {
    const fetchRewards = async () => {
      if (user?.walletAddress) {
        const balance = await balanceOf(user.walletAddress);
        setEarnedRewards(Number(balance));
      }
    };
    fetchRewards();
  }, [user, balanceOf]);

  const handleClaimRewards = async () => {
    try {
      await claimRewards(); // 假设 claimRewards 函数存在于钩子中
      alert('Rewards claimed successfully');
      // 更新余额
      if (user?.walletAddress) {
        const balance = await balanceOf(user.walletAddress);
        setEarnedRewards(Number(balance));
      }
    } catch (error) {
      console.error('Claim failed', error);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Community Member Dashboard</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Referrals</h3>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Gift className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Earned Rewards</h3>
              <p className="text-2xl font-bold">{earnedRewards} MLife</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Discussions</h3>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <BarChart2 className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Community Stats</h3>
              <p className="text-2xl font-bold">N/A</p>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="flex space-x-4">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Refer a Friend
          </button>
          <button onClick={handleClaimRewards} className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
            Claim Rewards
          </button>
          <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
            Join Discussion
          </button>
        </div>
      </div>
    </div>
  );
}