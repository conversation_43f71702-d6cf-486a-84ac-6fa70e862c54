import Link from 'next/link';
import Navigation from '@/components/layout/Navigation';
import {
  ArrowLeft,
  Building2,
  Book,
  FileText,
  Code,
  Zap,
  Shield,
  Users,
  Coins,
  ExternalLink
} from 'lucide-react';

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Book className="w-16 h-16 mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Documentation
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Everything you need to know about using ManageLife platform, 
            from getting started to advanced features.
          </p>
        </div>
      </section>

      {/* Quick Start */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Quick Start Guide</h2>
            
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">1</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Create Account</h3>
                <p className="text-gray-600">Sign up with email or connect your MetaMask wallet to get started.</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">2</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Choose Your Role</h3>
                <p className="text-gray-600">Select your role: Homeowner, Renter, Buyer, Portfolio Manager, or Community Member.</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">3</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Exploring</h3>
                <p className="text-gray-600">Browse properties, join community events, and start earning $MLIFE tokens.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Documentation Sections */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Documentation Sections</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive guides for all aspects of the ManageLife platform
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* User Guide */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">User Guide</h3>
              <p className="text-gray-600 mb-4">
                Complete guide for using the platform, managing your profile, and navigating different features.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Account setup and verification</li>
                <li>• Dashboard navigation</li>
                <li>• Profile management</li>
                <li>• Role switching</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* Property Management */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Building2 className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Property Management</h3>
              <p className="text-gray-600 mb-4">
                Learn how to list, manage, and tokenize properties on the ManageLife platform.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Property listing process</li>
                <li>• NFT tokenization</li>
                <li>• Rental management</li>
                <li>• Maintenance tracking</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* Blockchain Integration */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Blockchain Integration</h3>
              <p className="text-gray-600 mb-4">
                Understanding how blockchain technology powers the ManageLife ecosystem.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Wallet connection</li>
                <li>• NFT creation and trading</li>
                <li>• Smart contracts</li>
                <li>• Transaction history</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* Token Economy */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                <Coins className="w-6 h-6 text-yellow-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">$MLIFE Token Economy</h3>
              <p className="text-gray-600 mb-4">
                Learn about the $MLIFE token, how to earn rewards, and the platform economy.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Token earning mechanisms</li>
                <li>• Reward system</li>
                <li>• Token utility</li>
                <li>• Staking and governance</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* API Documentation */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <Code className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">API Documentation</h3>
              <p className="text-gray-600 mb-4">
                Technical documentation for developers integrating with ManageLife APIs.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Authentication</li>
                <li>• Property APIs</li>
                <li>• User management</li>
                <li>• Webhook integration</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>

            {/* Security */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Security & Privacy</h3>
              <p className="text-gray-600 mb-4">
                Information about platform security, privacy policies, and best practices.
              </p>
              <ul className="space-y-2 text-sm text-gray-600 mb-4">
                <li>• Security measures</li>
                <li>• Privacy policy</li>
                <li>• Data protection</li>
                <li>• Best practices</li>
              </ul>
              <Link href="#" className="text-blue-600 hover:text-blue-700 font-medium flex items-center">
                Read Guide <ExternalLink className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
            
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">What is property tokenization?</h3>
                <p className="text-gray-600">
                  Property tokenization is the process of converting real estate assets into digital tokens (NFTs) on the blockchain. 
                  This allows for fractional ownership, easier transfers, and transparent record-keeping.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">How do I earn $MLIFE tokens?</h3>
                <p className="text-gray-600">
                  You can earn $MLIFE tokens through various activities: timely rent payments, property transactions, 
                  community participation, referrals, and completing platform milestones.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Is my data secure on the platform?</h3>
                <p className="text-gray-600">
                  Yes, we use industry-standard encryption and security measures. Personal data is protected, 
                  while transaction records are stored securely on the blockchain for transparency.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Do I need a crypto wallet to use ManageLife?</h3>
                <p className="text-gray-600">
                  While you can create an account with just an email, connecting a crypto wallet (like MetaMask) 
                  unlocks full blockchain features including NFT trading and token management.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">What blockchain networks are supported?</h3>
                <p className="text-gray-600">
                  ManageLife currently supports Ethereum mainnet, Polygon, and Sepolia testnet. 
                  We're continuously evaluating additional networks based on user needs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Need More Help?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/support"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow"
            >
              Contact Support
            </Link>
            <Link
              href="/community"
              className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
            >
              Join Community
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
