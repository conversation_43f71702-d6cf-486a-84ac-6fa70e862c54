'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Navigation from '@/components/layout/Navigation';
import {
  Building2,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  FileText,
  Shield,
  DollarSign,
  Clock,
  AlertTriangle,
  Info,
  ExternalLink,
  Users,
  MapPin,
  Phone,
  Mail,
  Upload,
  Download
} from 'lucide-react';

interface LLCFormData {
  // Step 1: Basic Information
  companyName: string;
  businessPurpose: string;
  state: string;
  
  // Step 2: Registered Agent
  agentType: 'self' | 'service';
  agentName: string;
  agentAddress: string;
  agentCity: string;
  agentState: string;
  agentZip: string;
  agentPhone: string;
  agentEmail: string;
  
  // Step 3: Members/Owners
  members: Array<{
    name: string;
    address: string;
    ownershipPercentage: number;
    role: string;
  }>;
  
  // Step 4: Property Information
  propertyAddress: string;
  propertyValue: number;
  propertyType: string;
  purchaseDate: string;
  
  // Step 5: Operating Agreement
  managementStructure: 'member-managed' | 'manager-managed';
  profitDistribution: string;
  votingRights: string;
}

const US_STATES = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut',
  'Delaware', 'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa',
  'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan',
  'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire',
  'New Jersey', 'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio',
  'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina', 'South Dakota',
  'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia',
  'Wisconsin', 'Wyoming'
];

const RECOMMENDED_STATES = [
  { state: 'Delaware', pros: ['Business-friendly laws', 'Strong legal precedents', 'Privacy protection'], cons: ['Higher fees', 'Franchise tax'], cost: '$90' },
  { state: 'Wyoming', pros: ['Low fees', 'Strong asset protection', 'No state income tax'], cons: ['Less established case law'], cost: '$100' },
  { state: 'Nevada', pros: ['No state income tax', 'Strong privacy laws', 'Asset protection'], cons: ['Higher annual fees'], cost: '$75' },
  { state: 'Texas', pros: ['No state income tax', 'Business-friendly', 'Large real estate market'], cons: ['Franchise tax'], cost: '$300' },
];

export default function LLCSetupPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<LLCFormData>({
    companyName: '',
    businessPurpose: 'Real Estate Investment and Property Management',
    state: '',
    agentType: 'self',
    agentName: '',
    agentAddress: '',
    agentCity: '',
    agentState: '',
    agentZip: '',
    agentPhone: '',
    agentEmail: '',
    members: [{ name: '', address: '', ownershipPercentage: 100, role: 'Managing Member' }],
    propertyAddress: '',
    propertyValue: 0,
    propertyType: 'residential',
    purchaseDate: '',
    managementStructure: 'member-managed',
    profitDistribution: 'proportional',
    votingRights: 'proportional'
  });

  const totalSteps = 6;

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Here you would integrate with LLC formation service API
    console.log('LLC Formation Data:', formData);
    // Redirect to property tokenization after LLC setup
    router.push('/tokenize/property-setup');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center space-x-4 mb-4">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-blue-600 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </button>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">LLC Formation for Real Estate Tokenization</h1>
            <p className="text-gray-600 mt-2">
              Set up your Limited Liability Company to legally own and tokenize your property
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600">Step {currentStep} of {totalSteps}</span>
            <span className="text-sm text-gray-500">{Math.round((currentStep / totalSteps) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              {/* Step 1: Why LLC? */}
              {currentStep === 1 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Why Form an LLC for Real Estate?</h2>
                  
                  <div className="space-y-6 mb-8">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Shield className="w-6 h-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Asset Protection</h3>
                        <p className="text-gray-600">
                          Protect your personal assets from lawsuits and liabilities related to your property. 
                          The LLC creates a legal barrier between your personal wealth and business risks.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <DollarSign className="w-6 h-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Tax Benefits</h3>
                        <p className="text-gray-600">
                          Enjoy pass-through taxation, deduct property expenses, depreciation, and avoid double taxation. 
                          LLCs offer flexible tax elections (sole proprietorship, partnership, or corporation).
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Building2 className="w-6 h-6 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Tokenization Ready</h3>
                        <p className="text-gray-600">
                          LLCs provide the legal structure needed for property tokenization. The LLC can issue 
                          membership interests that correspond to NFT ownership, creating a compliant bridge to blockchain.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Users className="w-6 h-6 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Flexible Ownership</h3>
                        <p className="text-gray-600">
                          Allow multiple investors, easy transfer of ownership interests, and flexible management 
                          structures. Perfect for fractional real estate investment.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800">Important Legal Requirement</h4>
                        <p className="text-sm text-yellow-700 mt-1">
                          To tokenize real estate on ManageLife, your property must be owned by an LLC. 
                          This ensures legal compliance and proper ownership structure for blockchain representation.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={nextStep}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
                    >
                      Start LLC Formation
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 2: Choose State */}
              {currentStep === 2 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Choose Formation State</h2>
                  
                  <div className="mb-6">
                    <p className="text-gray-600 mb-4">
                      Select the state where you want to form your LLC. Consider factors like filing fees, 
                      annual costs, tax implications, and legal protections.
                    </p>
                  </div>

                  {/* Recommended States */}
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommended for Real Estate</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {RECOMMENDED_STATES.map((stateInfo) => (
                        <div
                          key={stateInfo.state}
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            formData.state === stateInfo.state
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => updateFormData('state', stateInfo.state)}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold text-gray-900">{stateInfo.state}</h4>
                            <span className="text-sm font-medium text-green-600">{stateInfo.cost}</span>
                          </div>
                          <div className="space-y-2">
                            <div>
                              <p className="text-xs font-medium text-green-700 mb-1">Pros:</p>
                              <ul className="text-xs text-gray-600 space-y-1">
                                {stateInfo.pros.map((pro, index) => (
                                  <li key={index} className="flex items-center">
                                    <CheckCircle className="w-3 h-3 text-green-500 mr-1" />
                                    {pro}
                                  </li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-red-700 mb-1">Cons:</p>
                              <ul className="text-xs text-gray-600 space-y-1">
                                {stateInfo.cons.map((con, index) => (
                                  <li key={index} className="flex items-center">
                                    <AlertTriangle className="w-3 h-3 text-red-500 mr-1" />
                                    {con}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* All States Dropdown */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Or choose any state:
                    </label>
                    <select
                      value={formData.state}
                      onChange={(e) => updateFormData('state', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a state...</option>
                      {US_STATES.map((state) => (
                        <option key={state} value={state}>{state}</option>
                      ))}
                    </select>
                  </div>

                  <div className="flex justify-between">
                    <button
                      onClick={prevStep}
                      className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                    <button
                      onClick={nextStep}
                      disabled={!formData.state}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 3: Company Name & Purpose */}
              {currentStep === 3 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Company Name & Business Purpose</h2>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        LLC Name *
                      </label>
                      <input
                        type="text"
                        value={formData.companyName}
                        onChange={(e) => updateFormData('companyName', e.target.value)}
                        placeholder="e.g., Sunset Properties LLC"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Must end with "LLC" or "Limited Liability Company"
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Business Purpose
                      </label>
                      <textarea
                        value={formData.businessPurpose}
                        onChange={(e) => updateFormData('businessPurpose', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Describe the primary business activities of your LLC
                      </p>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-blue-800">Name Requirements</h4>
                          <ul className="text-sm text-blue-700 mt-1 space-y-1">
                            <li>• Must be unique in your chosen state</li>
                            <li>• Cannot contain restricted words (Bank, Insurance, etc.)</li>
                            <li>• Must include "LLC" or "Limited Liability Company"</li>
                            <li>• Cannot be misleading about business activities</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      onClick={prevStep}
                      className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                    <button
                      onClick={nextStep}
                      disabled={!formData.companyName}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 4: Registered Agent */}
              {currentStep === 4 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Registered Agent</h2>

                  <div className="mb-6">
                    <p className="text-gray-600">
                      A registered agent receives legal documents and official correspondence on behalf of your LLC.
                      They must have a physical address in your formation state and be available during business hours.
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Choose Registered Agent Type
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            formData.agentType === 'self'
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => updateFormData('agentType', 'self')}
                        >
                          <h4 className="font-semibold text-gray-900 mb-2">Serve as My Own Agent</h4>
                          <p className="text-sm text-gray-600 mb-3">
                            You or someone you know serves as the registered agent
                          </p>
                          <div className="text-xs text-gray-500">
                            <p>✓ Free option</p>
                            <p>✗ Must be available during business hours</p>
                            <p>✗ Your name/address becomes public record</p>
                          </div>
                        </div>

                        <div
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            formData.agentType === 'service'
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => updateFormData('agentType', 'service')}
                        >
                          <h4 className="font-semibold text-gray-900 mb-2">Professional Service</h4>
                          <p className="text-sm text-gray-600 mb-3">
                            Use a professional registered agent service
                          </p>
                          <div className="text-xs text-gray-500">
                            <p>✓ Privacy protection</p>
                            <p>✓ Always available</p>
                            <p>✓ Document scanning/forwarding</p>
                            <p className="text-orange-600">~ $100-300/year</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {formData.agentType === 'self' && (
                      <div className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Agent Name *
                            </label>
                            <input
                              type="text"
                              value={formData.agentName}
                              onChange={(e) => updateFormData('agentName', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Phone Number *
                            </label>
                            <input
                              type="tel"
                              value={formData.agentPhone}
                              onChange={(e) => updateFormData('agentPhone', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Street Address *
                          </label>
                          <input
                            type="text"
                            value={formData.agentAddress}
                            onChange={(e) => updateFormData('agentAddress', e.target.value)}
                            placeholder="Physical address (no P.O. boxes)"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>

                        <div className="grid md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              City *
                            </label>
                            <input
                              type="text"
                              value={formData.agentCity}
                              onChange={(e) => updateFormData('agentCity', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              State *
                            </label>
                            <select
                              value={formData.agentState}
                              onChange={(e) => updateFormData('agentState', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                              <option value="">Select state</option>
                              {US_STATES.map((state) => (
                                <option key={state} value={state}>{state}</option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              ZIP Code *
                            </label>
                            <input
                              type="text"
                              value={formData.agentZip}
                              onChange={(e) => updateFormData('agentZip', e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {formData.agentType === 'service' && (
                      <div className="bg-gray-50 rounded-lg p-6 text-center">
                        <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">Professional Service Integration</h4>
                        <p className="text-gray-600 mb-4">
                          We'll connect you with trusted registered agent services in your chosen state.
                        </p>
                        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                          View Service Options
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      onClick={prevStep}
                      className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                    <button
                      onClick={nextStep}
                      disabled={formData.agentType === 'self' && (!formData.agentName || !formData.agentAddress)}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 5: Property Information */}
              {currentStep === 5 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Property Information</h2>

                  <div className="mb-6">
                    <p className="text-gray-600">
                      Provide details about the property that will be owned by your LLC and tokenized on the blockchain.
                    </p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Property Address *
                      </label>
                      <input
                        type="text"
                        value={formData.propertyAddress}
                        onChange={(e) => updateFormData('propertyAddress', e.target.value)}
                        placeholder="123 Main Street, City, State, ZIP"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Property Type *
                        </label>
                        <select
                          value={formData.propertyType}
                          onChange={(e) => updateFormData('propertyType', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="residential">Residential</option>
                          <option value="commercial">Commercial</option>
                          <option value="industrial">Industrial</option>
                          <option value="mixed-use">Mixed Use</option>
                          <option value="land">Land</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Purchase Date *
                        </label>
                        <input
                          type="date"
                          value={formData.purchaseDate}
                          onChange={(e) => updateFormData('purchaseDate', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Property Value (USD) *
                      </label>
                      <input
                        type="number"
                        value={formData.propertyValue}
                        onChange={(e) => updateFormData('propertyValue', parseFloat(e.target.value))}
                        placeholder="500000"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        Current market value or recent appraisal value
                      </p>
                    </div>

                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-green-800">Property Transfer Process</h4>
                          <p className="text-sm text-green-700 mt-1">
                            After LLC formation, you'll need to transfer property ownership from your personal name
                            to the LLC through a quitclaim deed or warranty deed. We'll provide guidance on this process.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      onClick={prevStep}
                      className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                    <button
                      onClick={nextStep}
                      disabled={!formData.propertyAddress || !formData.propertyValue}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 6: Review & Submit */}
              {currentStep === 6 && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Review & Submit</h2>

                  <div className="space-y-6">
                    {/* Company Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Company Name:</span>
                          <p className="font-medium">{formData.companyName}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Formation State:</span>
                          <p className="font-medium">{formData.state}</p>
                        </div>
                        <div className="md:col-span-2">
                          <span className="text-gray-600">Business Purpose:</span>
                          <p className="font-medium">{formData.businessPurpose}</p>
                        </div>
                      </div>
                    </div>

                    {/* Registered Agent */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Registered Agent</h3>
                      <div className="text-sm">
                        {formData.agentType === 'self' ? (
                          <div className="space-y-2">
                            <p><span className="text-gray-600">Name:</span> <span className="font-medium">{formData.agentName}</span></p>
                            <p><span className="text-gray-600">Address:</span> <span className="font-medium">{formData.agentAddress}, {formData.agentCity}, {formData.agentState} {formData.agentZip}</span></p>
                            <p><span className="text-gray-600">Phone:</span> <span className="font-medium">{formData.agentPhone}</span></p>
                          </div>
                        ) : (
                          <p className="font-medium">Professional registered agent service will be arranged</p>
                        )}
                      </div>
                    </div>

                    {/* Property Information */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Information</h3>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div className="md:col-span-2">
                          <span className="text-gray-600">Address:</span>
                          <p className="font-medium">{formData.propertyAddress}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Type:</span>
                          <p className="font-medium capitalize">{formData.propertyType}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Value:</span>
                          <p className="font-medium">${formData.propertyValue.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Purchase Date:</span>
                          <p className="font-medium">{formData.purchaseDate}</p>
                        </div>
                      </div>
                    </div>

                    {/* Next Steps */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-blue-900 mb-4">What Happens Next?</h3>
                      <div className="space-y-3 text-sm text-blue-800">
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                          <p>We'll file your Articles of Organization with the state</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                          <p>Obtain your EIN (Tax ID) from the IRS</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                          <p>Prepare your Operating Agreement</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">4</div>
                          <p>Guide you through property transfer to LLC</p>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">5</div>
                          <p>Enable property tokenization on ManageLife platform</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="text-sm font-medium text-yellow-800">Important Legal Notice</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            This service provides assistance with LLC formation. For complex legal matters,
                            consult with a qualified attorney. Property transfer and tokenization involve
                            additional legal and regulatory considerations.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <button
                      onClick={prevStep}
                      className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>
                    <button
                      onClick={handleSubmit}
                      className="bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center"
                    >
                      Submit LLC Formation
                      <CheckCircle className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Cost Breakdown */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Estimated Costs</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">State Filing Fee</span>
                  <span className="font-medium">$75 - $500</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Registered Agent</span>
                  <span className="font-medium">$0 - $300/year</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Operating Agreement</span>
                  <span className="font-medium">$200 - $800</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">EIN (Tax ID)</span>
                  <span className="font-medium text-green-600">Free</span>
                </div>
                <hr className="border-gray-200" />
                <div className="flex justify-between font-semibold">
                  <span>Total Estimated</span>
                  <span>$275 - $1,600</span>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Formation Timeline</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">1-3 Business Days</p>
                    <p className="text-sm text-gray-600">State processing time</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <FileText className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-gray-900">Same Day</p>
                    <p className="text-sm text-gray-600">EIN application</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Building2 className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="font-medium text-gray-900">1-2 Days</p>
                    <p className="text-sm text-gray-600">Bank account opening</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Help */}
            <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help?</h3>
              <p className="text-blue-700 text-sm mb-4">
                Our legal experts can help you navigate the LLC formation process and ensure compliance.
              </p>
              <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                Speak with Expert
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
