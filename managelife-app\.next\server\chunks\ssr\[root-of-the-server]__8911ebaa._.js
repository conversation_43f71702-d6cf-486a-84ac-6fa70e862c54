module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "useAuth": ()=>useAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Check if user is authenticated on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        checkAuth();
    }, []);
    const checkAuth = async ()=>{
        try {
            const response = await fetch('/api/auth/me');
            if (response.ok) {
                const data = await response.json();
                setUser({
                    ...data.user,
                    joinedAt: new Date(data.user.joinedAt)
                });
            }
        } catch (error) {
            console.error('Auth check failed:', error);
        } finally{
            setLoading(false);
        }
    };
    const login = async (email, password)=>{
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email,
                password
            })
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.error || 'Login failed');
        }
        setUser({
            ...data.user,
            joinedAt: new Date(data.user.joinedAt)
        });
    };
    const loginWithWallet = async (walletAddress)=>{
        const response = await fetch('/api/auth/wallet', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                walletAddress
            })
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.error || 'Wallet login failed');
        }
        setUser({
            ...data.user,
            joinedAt: new Date(data.user.joinedAt)
        });
        return {
            isNewUser: data.isNewUser
        };
    };
    const register = async (userData)=>{
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.error || 'Registration failed');
        }
        setUser({
            ...data.user,
            joinedAt: new Date(data.user.joinedAt)
        });
    };
    const logout = async ()=>{
        try {
            await fetch('/api/auth/logout', {
                method: 'POST'
            });
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            setUser(null);
        }
    };
    const updateUser = async (userData)=>{
        const response = await fetch('/api/auth/me', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        const data = await response.json();
        if (!response.ok) {
            throw new Error(data.error || 'Update failed');
        }
        setUser({
            ...data.user,
            joinedAt: new Date(data.user.joinedAt)
        });
    };
    const refreshUser = async ()=>{
        await checkAuth();
    };
    const value = {
        user,
        loading,
        login,
        loginWithWallet,
        register,
        logout,
        updateUser,
        updateProfile: updateUser,
        refreshUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 173,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
}),
"[externals]/node:crypto [external] (node:crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/lib/web3.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CHAIN_CONFIG": ()=>CHAIN_CONFIG,
    "CONTRACT_ABIS": ()=>CONTRACT_ABIS,
    "CONTRACT_ADDRESSES": ()=>CONTRACT_ADDRESSES,
    "GAS_LIMITS": ()=>GAS_LIMITS,
    "SUPPORTED_CHAINS": ()=>SUPPORTED_CHAINS,
    "Web3Error": ()=>Web3Error,
    "config": ()=>config,
    "formatTokenAmount": ()=>formatTokenAmount,
    "getContractAddress": ()=>getContractAddress,
    "parseTokenAmount": ()=>parseTokenAmount
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$mainnet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/chains/definitions/mainnet.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$sepolia$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/chains/definitions/sepolia.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/chains/definitions/polygon.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygonMumbai$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/chains/definitions/polygonMumbai.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-ssr] (ecmascript) <locals>");
;
;
// Project configuration
const projectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'demo-project-id';
const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultConfig"])({
    appName: 'ManageLife',
    projectId,
    chains: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$mainnet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mainnet"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$sepolia$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sepolia"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygon"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygonMumbai$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygonMumbai"]
    ],
    ssr: true
});
const CONTRACT_ADDRESSES = {
    // Sepolia testnet addresses (for development)
    SEPOLIA: {
        NFTi: '0x0000000000000000000000000000000000000001',
        NFTr: '0x0000000000000000000000000000000000000002',
        MLIFE_TOKEN: '0x0000000000000000000000000000000000000003',
        MARKETPLACE: '0x0000000000000000000000000000000000000004',
        PROPERTY_REGISTRY: '0x0000000000000000000000000000000000000005'
    },
    // Polygon mainnet addresses (for production)
    POLYGON: {
        NFTi: '0x0000000000000000000000000000000000000001',
        NFTr: '0x0000000000000000000000000000000000000002',
        MLIFE_TOKEN: '0x0000000000000000000000000000000000000003',
        MARKETPLACE: '0x0000000000000000000000000000000000000004',
        PROPERTY_REGISTRY: '0x0000000000000000000000000000000000000005'
    }
};
const CONTRACT_ABIS = {
    // ERC721 NFT ABI (simplified)
    NFT: [
        'function mint(address to, uint256 tokenId, string memory tokenURI) public',
        'function ownerOf(uint256 tokenId) public view returns (address)',
        'function tokenURI(uint256 tokenId) public view returns (string)',
        'function approve(address to, uint256 tokenId) public',
        'function transferFrom(address from, address to, uint256 tokenId) public',
        'function balanceOf(address owner) public view returns (uint256)',
        'event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)'
    ],
    // ERC20 Token ABI (simplified)
    ERC20: [
        'function balanceOf(address account) public view returns (uint256)',
        'function transfer(address to, uint256 amount) public returns (bool)',
        'function approve(address spender, uint256 amount) public returns (bool)',
        'function allowance(address owner, address spender) public view returns (uint256)',
        'function totalSupply() public view returns (uint256)',
        'function decimals() public view returns (uint8)',
        'function symbol() public view returns (string)',
        'function name() public view returns (string)',
        'event Transfer(address indexed from, address indexed to, uint256 value)',
        'event Approval(address indexed owner, address indexed spender, uint256 value)'
    ],
    // Marketplace ABI (simplified)
    MARKETPLACE: [
        'function listProperty(uint256 tokenId, uint256 price, bool isForRent) public',
        'function buyProperty(uint256 listingId) public payable',
        'function rentProperty(uint256 listingId, uint256 duration) public payable',
        'function cancelListing(uint256 listingId) public',
        'function getListing(uint256 listingId) public view returns (tuple(uint256 tokenId, address seller, uint256 price, bool isForRent, bool isActive))',
        'event PropertyListed(uint256 indexed listingId, uint256 indexed tokenId, address indexed seller, uint256 price, bool isForRent)',
        'event PropertySold(uint256 indexed listingId, address indexed buyer)',
        'event PropertyRented(uint256 indexed listingId, address indexed renter, uint256 duration)'
    ],
    // Property Registry ABI (simplified)
    PROPERTY_REGISTRY: [
        'function registerProperty(string memory propertyData, string memory location) public returns (uint256)',
        'function getProperty(uint256 propertyId) public view returns (tuple(string propertyData, string location, address owner, bool isTokenized))',
        'function tokenizeProperty(uint256 propertyId, string memory tokenURI) public returns (uint256)',
        'function updateProperty(uint256 propertyId, string memory propertyData) public',
        'event PropertyRegistered(uint256 indexed propertyId, address indexed owner)',
        'event PropertyTokenized(uint256 indexed propertyId, uint256 indexed tokenId)'
    ]
};
function getContractAddress(contractName, chainId) {
    switch(chainId){
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$sepolia$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sepolia"].id:
            return CONTRACT_ADDRESSES.SEPOLIA[contractName];
        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygon"].id:
            return CONTRACT_ADDRESSES.POLYGON[contractName];
        default:
            return CONTRACT_ADDRESSES.SEPOLIA[contractName]; // Default to Sepolia for development
    }
}
const SUPPORTED_CHAINS = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$sepolia$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sepolia"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygon"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygonMumbai$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygonMumbai"]
];
const CHAIN_CONFIG = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$sepolia$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sepolia"].id]: {
        name: 'Sepolia Testnet',
        currency: 'ETH',
        blockExplorer: 'https://sepolia.etherscan.io',
        isTestnet: true
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygon"].id]: {
        name: 'Polygon',
        currency: 'MATIC',
        blockExplorer: 'https://polygonscan.com',
        isTestnet: false
    },
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$chains$2f$definitions$2f$polygonMumbai$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["polygonMumbai"].id]: {
        name: 'Polygon Mumbai',
        currency: 'MATIC',
        blockExplorer: 'https://mumbai.polygonscan.com',
        isTestnet: true
    }
};
const GAS_LIMITS = {
    MINT_NFT: 200000,
    TRANSFER_NFT: 100000,
    LIST_PROPERTY: 150000,
    BUY_PROPERTY: 200000,
    RENT_PROPERTY: 180000,
    TOKEN_TRANSFER: 65000,
    TOKEN_APPROVE: 50000
};
class Web3Error extends Error {
    code;
    data;
    constructor(message, code, data){
        super(message), this.code = code, this.data = data;
        this.name = 'Web3Error';
    }
}
function formatTokenAmount(amount, decimals = 18) {
    const divisor = BigInt(10 ** decimals);
    const quotient = amount / divisor;
    const remainder = amount % divisor;
    if (remainder === 0n) {
        return quotient.toString();
    }
    const remainderStr = remainder.toString().padStart(decimals, '0');
    const trimmedRemainder = remainderStr.replace(/0+$/, '');
    if (trimmedRemainder === '') {
        return quotient.toString();
    }
    return `${quotient}.${trimmedRemainder}`;
}
function parseTokenAmount(amount, decimals = 18) {
    const [whole, fraction = ''] = amount.split('.');
    const paddedFraction = fraction.padEnd(decimals, '0').slice(0, decimals);
    return BigInt(whole + paddedFraction);
}
}),
"[project]/src/components/providers/Web3Provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Web3Provider": ()=>Web3Provider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/wagmi/dist/esm/context.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rainbow-me/rainbowkit/dist/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$chunk$2d$RZWDCITT$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rainbow-me/rainbowkit/dist/chunk-RZWDCITT.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$chunk$2d$72HZGUJA$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rainbow-me/rainbowkit/dist/chunk-72HZGUJA.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/web3.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]();
function Web3Provider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WagmiProvider"], {
        config: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["config"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
            client: queryClient,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["RainbowKitProvider"], {
                theme: {
                    lightMode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$chunk$2d$72HZGUJA$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["lightTheme"])({
                        accentColor: '#3b82f6',
                        accentColorForeground: 'white',
                        borderRadius: 'medium',
                        fontStack: 'system'
                    }),
                    darkMode: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rainbow$2d$me$2f$rainbowkit$2f$dist$2f$chunk$2d$RZWDCITT$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["darkTheme"])({
                        accentColor: '#3b82f6',
                        accentColorForeground: 'white',
                        borderRadius: 'medium',
                        fontStack: 'system'
                    })
                },
                appInfo: {
                    appName: 'ManageLife',
                    learnMoreUrl: 'https://managelife.com/learn'
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/providers/Web3Provider.tsx",
                lineNumber: 22,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/providers/Web3Provider.tsx",
            lineNumber: 21,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/providers/Web3Provider.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8911ebaa._.js.map