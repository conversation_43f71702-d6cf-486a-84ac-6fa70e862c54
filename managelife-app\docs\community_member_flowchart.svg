<svg width="600" height="800" viewBox="0 0 600 800" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node { fill: lightblue; stroke: black; stroke-width: 1; }
    .start-end { fill: lightgreen; }
    .decision { fill: yellow; }
    .text { font: 12px sans-serif; text-anchor: middle; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
    .title { font: 16px sans-serif; text-anchor: middle; font-weight: bold; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>
  <!-- Role Title -->
  <text x="300" y="20" class="title">Community Member Role</text>
  <!-- Start -->
  <ellipse cx="300" cy="50" rx="80" ry="30" class="start-end node" />
  <text x="300" y="55" class="text">Start: Login/Register</text>
  <!-- Arrow to Join Community -->
  <line x1="300" y1="80" x2="300" y2="130" class="arrow" />
  <!-- Join Community -->
  <rect x="220" y="130" width="160" height="40" class="node" />
  <text x="300" y="150" class="text">Join Community</text>
  <!-- Arrow to Participate in Events -->
  <line x1="300" y1="170" x2="300" y2="220" class="arrow" />
  <!-- Participate in Events -->
  <rect x="220" y="220" width="160" height="40" class="node" />
  <text x="300" y="240" class="text">Participate in Events</text>
  <!-- Arrow to Refer Leads -->
  <line x1="300" y1="260" x2="300" y2="310" class="arrow" />
  <!-- Refer Leads -->
  <rect x="220" y="310" width="160" height="40" class="node" />
  <text x="300" y="330" class="text">Refer Leads</text>
  <!-- Arrow to Earn Rewards -->
  <line x1="300" y1="350" x2="300" y2="400" class="arrow" />
  <!-- Earn Rewards -->
  <rect x="220" y="400" width="160" height="40" class="node" />
  <text x="300" y="420" class="text">Earn Rewards</text>
  <!-- Arrow to Decision: Redeem? -->
  <line x1="300" y1="440" x2="300" y2="490" class="arrow" />
  <!-- Decision: Redeem? -->
  <polygon points="300,490 360,520 300,550 240,520" class="decision node" />
  <text x="300" y="525" class="text">Redeem Rewards?</text>
  <!-- Arrow Yes to Redeem Rewards -->
  <line x1="360" y1="520" x2="450" y2="520" class="arrow" />
  <text x="405" y="510" class="text">Yes</text>
  <line x1="450" y1="520" x2="450" y2="570" class="arrow" />
  <!-- Redeem Rewards -->
  <rect x="370" y="570" width="160" height="40" class="node" />
  <text x="450" y="590" class="text">Redeem Rewards</text>
  <!-- Arrow to End -->
  <line x1="450" y1="610" x2="450" y2="660" class="arrow" />
  <!-- End -->
  <ellipse cx="450" cy="690" rx="80" ry="30" class="start-end node" />
  <text x="450" y="695" class="text">End</text>
  <!-- Arrow No back to Participate -->
  <line x1="240" y1="520" x2="150" y2="520" class="arrow" />
  <text x="195" y="510" class="text">No</text>
  <line x1="150" y1="520" x2="150" y2="240" class="arrow" />
  <line x1="150" y1="240" x2="220" y2="240" class="arrow" />
</svg>