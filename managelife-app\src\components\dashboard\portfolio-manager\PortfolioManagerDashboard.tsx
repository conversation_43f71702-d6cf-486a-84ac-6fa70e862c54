import React, { useState, useEffect } from 'react';
import { Briefcase, BarChart2, Wallet, AlertCircle, TrendingUp, TrendingDown, DollarSign, Building2, Plus, Eye, Edit, Trash2, MapPin, Users, Coins } from 'lucide-react';

import { useAuth } from '@/contexts/AuthContext';
import { usePropertyRegistry } from '@/hooks/useWeb3';
import { Property } from '@/types';

interface PortfolioStats {
  totalValue: number;
  totalInvestment: number;
  totalReturn: number;
  monthlyIncome: number;
  averageROI: number;
  propertiesCount: number;
  occupancyRate: number;
}

export default function PortfolioManagerDashboard() {
  const { user } = useAuth();
  const { registerProperty, tokenizeProperty } = usePropertyRegistry();
  const [properties, setProperties] = useState<Property[]>([]);
  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats>({
    totalValue: 0,
    totalInvestment: 0,
    totalReturn: 0,
    monthlyIncome: 0,
    averageROI: 0,
    propertiesCount: 0,
    occupancyRate: 0,
  });
  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');
  const [sortBy, setSortBy] = useState<'value' | 'roi' | 'income' | 'date'>('value');

  // 模拟获取数据或集成真实数据
  useEffect(() => {
    // 这里应集成真实数据源，如从合约获取
    const mockProperties = [
      // 类似 portfolio/page.tsx 中的 mock 数据
      {
        id: '1',
        title: 'Luxury Downtown Apartment',
        location: 'Manhattan, NY',
        type: 'residential',
        value: 850000,
        purchasePrice: 720000,
        purchaseDate: '2023-03-15',
        monthlyIncome: 6500,
        occupancyRate: 100,
        roi: 15.2,
        status: 'active',
        image: '/api/placeholder/300/200',
        tenants: 1,
        maxTenants: 1,
      },
      // 添加更多...
    ];
    setProperties(mockProperties);

    // 计算统计
    const stats = mockProperties.reduce((acc, prop) => ({
      totalValue: acc.totalValue + prop.value,
      totalInvestment: acc.totalInvestment + prop.purchasePrice,
      totalReturn: acc.totalReturn + (prop.value - prop.purchasePrice),
      monthlyIncome: acc.monthlyIncome + prop.monthlyIncome,
      averageROI: (acc.averageROI + prop.roi) / (acc.propertiesCount + 1),
      propertiesCount: acc.propertiesCount + 1,
      occupancyRate: (acc.occupancyRate + prop.occupancyRate) / (acc.propertiesCount + 1),
    }), { totalValue: 0, totalInvestment: 0, totalReturn: 0, monthlyIncome: 0, averageROI: 0, propertiesCount: 0, occupancyRate: 0 });
    setPortfolioStats(stats);
  }, []);

  const filteredProperties = properties.filter(property => filterType === 'all' || property.type === filterType);

  const sortedProperties = [...filteredProperties].sort((a, b) => {
    switch (sortBy) {
      case 'value': return b.value - a.value;
      case 'roi': return b.roi - a.roi;
      case 'income': return b.monthlyIncome - a.monthlyIncome;
      case 'date': return new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime();
      default: return 0;
    }
  });

  const handleUpdateAsset = async (propertyId: string) => {
    // 实现更新逻辑，使用 registerProperty 或 tokenizeProperty
    try {
      await tokenizeProperty(Number(propertyId), 'mockURI');
      alert('Asset updated successfully');
    } catch (error) {
      console.error('Update failed', error);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Portfolio Manager Dashboard</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Portfolio Value</p>
              <p className="text-2xl font-bold text-gray-900">${portfolioStats.totalValue.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 font-medium">+12.5%</span>
            <span className="text-sm text-gray-500 ml-1">vs last month</span>
          </div>
        </div>
        {/* 类似添加其他统计卡片，使用 portfolioStats */}
      </div>
      {/* 添加属性列表、过滤、排序和更新按钮，类似 portfolio/page.tsx */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Properties</h3>
        {/* 过滤和排序选择 */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedProperties.map((property) => (
            <div key={property.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              {/* 属性详情 */}
              <button onClick={() => handleUpdateAsset(property.id)} className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                Update Asset
              </button>
            </div>
          ))}
        </div>
      </div>
      {/* 添加监控部分，如警报列表 */}
    </div>
  );
}