<svg width="600" height="800" viewBox="0 0 600 800" xmlns="http://www.w3.org/2000/svg">
  <style>
    .node { fill: lightblue; stroke: black; stroke-width: 1; }
    .start-end { fill: lightgreen; }
    .decision { fill: yellow; }
    .text { font: 12px sans-serif; text-anchor: middle; }
    .arrow { stroke: black; stroke-width: 1; marker-end: url(#arrowhead); }
    .title { font: 16px sans-serif; text-anchor: middle; font-weight: bold; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" />
    </marker>
  </defs>
  <!-- Role Title -->
  <text x="300" y="20" class="title">Portfolio Manager Role</text>
  <!-- Start -->
  <ellipse cx="300" cy="50" rx="80" ry="30" class="start-end node" />
  <text x="300" y="55" class="text">Start: Login/Register</text>
  <!-- Arrow to Dashboard -->
  <line x1="300" y1="80" x2="300" y2="130" class="arrow" />
  <!-- Dashboard -->
  <rect x="220" y="130" width="160" height="40" class="node" />
  <text x="300" y="150" class="text">Dashboard</text>
  <!-- Arrow to Manage Properties -->
  <line x1="300" y1="170" x2="300" y2="220" class="arrow" />
  <!-- Manage Properties -->
  <rect x="220" y="220" width="160" height="40" class="node" />
  <text x="300" y="240" class="text">Manage Properties</text>
  <!-- Arrow to Monitor Performance -->
  <line x1="300" y1="260" x2="300" y2="310" class="arrow" />
  <!-- Monitor Performance -->
  <rect x="220" y="310" width="160" height="40" class="node" />
  <text x="300" y="330" class="text">Monitor Performance</text>
  <!-- Arrow to Generate Reports -->
  <line x1="300" y1="350" x2="300" y2="400" class="arrow" />
  <!-- Generate Reports -->
  <rect x="220" y="400" width="160" height="40" class="node" />
  <text x="300" y="420" class="text">Generate Reports</text>
  <!-- Arrow to Decision: Adjust? -->
  <line x1="300" y1="440" x2="300" y2="490" class="arrow" />
  <!-- Decision: Adjust? -->
  <polygon points="300,490 360,520 300,550 240,520" class="decision node" />
  <text x="300" y="525" class="text">Adjust Portfolio?</text>
  <!-- Arrow Yes to Adjust Portfolio -->
  <line x1="360" y1="520" x2="450" y2="520" class="arrow" />
  <text x="405" y="510" class="text">Yes</text>
  <line x1="450" y1="520" x2="450" y2="570" class="arrow" />
  <!-- Adjust Portfolio -->
  <rect x="370" y="570" width="160" height="40" class="node" />
  <text x="450" y="590" class="text">Adjust Portfolio</text>
  <!-- Arrow to End -->
  <line x1="450" y1="610" x2="450" y2="660" class="arrow" />
  <!-- End -->
  <ellipse cx="450" cy="690" rx="80" ry="30" class="start-end node" />
  <text x="450" y="695" class="text">End</text>
  <!-- Arrow No back to Monitor -->
  <line x1="240" y1="520" x2="150" y2="520" class="arrow" />
  <text x="195" y="510" class="text">No</text>
  <line x1="150" y1="520" x2="150" y2="330" class="arrow" />
  <line x1="150" y1="330" x2="220" y2="330" class="arrow" />
</svg>