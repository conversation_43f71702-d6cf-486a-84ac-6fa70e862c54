{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Building2, Menu, X, User, LogOut } from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface NavigationProps {\n  showAuthButtons?: boolean;\n  transparent?: boolean;\n  className?: string;\n}\n\nexport default function Navigation({ \n  showAuthButtons = true, \n  transparent = false,\n  className = \"\"\n}: NavigationProps) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navLinks = [\n    { href: '/marketplace', label: 'Marketplace' },\n    { href: '/tokenize', label: 'Tokenize Property' },\n    { href: '/about', label: 'Team' },\n    { href: '/community', label: 'Community' },\n    { href: '/docs', label: 'Docs' },\n    { href: '/blog', label: 'Blog' },\n  ];\n\n  const externalLinks = [\n    { href: 'https://managelife.io', label: 'Solutions' },\n    { href: 'https://managelife.io', label: 'Official Site' },\n  ];\n\n  const baseClasses = transparent \n    ? \"absolute top-0 left-0 right-0 z-50 bg-transparent\" \n    : \"bg-white shadow-sm border-b border-gray-200\";\n\n  return (\n    <header className={`${baseClasses} ${className}`}>\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navLinks.map((link) => (\n              <Link\n                key={link.href}\n                href={link.href}\n                className={`${\n                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                } transition-colors`}\n              >\n                {link.label}\n              </Link>\n            ))}\n            {externalLinks.map((link) => (\n              <a\n                key={link.href}\n                href={link.href}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className={`${\n                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                } transition-colors`}\n              >\n                {link.label}\n              </a>\n            ))}\n          </nav>\n\n          {/* Auth Buttons / User Menu */}\n          <div className=\"flex items-center space-x-4\">\n            {showAuthButtons && (\n              <>\n                {user ? (\n                  <div className=\"flex items-center space-x-4\">\n                    <Link\n                      href=\"/dashboard\"\n                      className={`flex items-center space-x-2 ${\n                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                      } transition-colors`}\n                    >\n                      <User className=\"w-4 h-4\" />\n                      <span className=\"hidden sm:inline\">Dashboard</span>\n                    </Link>\n                    <button\n                      onClick={handleLogout}\n                      className={`flex items-center space-x-2 ${\n                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                      } transition-colors`}\n                    >\n                      <LogOut className=\"w-4 h-4\" />\n                      <span className=\"hidden sm:inline\">Logout</span>\n                    </button>\n                  </div>\n                ) : (\n                  <>\n                    <Link\n                      href=\"/auth/login\"\n                      className={`${\n                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                      } transition-colors`}\n                    >\n                      Sign In\n                    </Link>\n                    <Link\n                      href=\"/auth/register\"\n                      className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow\"\n                    >\n                      Get Started\n                    </Link>\n                  </>\n                )}\n              </>\n            )}\n\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              className={`md:hidden ${\n                transparent ? 'text-white' : 'text-gray-600'\n              }`}\n            >\n              {mobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className={`md:hidden py-4 border-t ${\n            transparent ? 'border-white/20 bg-black/20 backdrop-blur-sm' : 'border-gray-200'\n          }`}>\n            <div className=\"flex flex-col space-y-4\">\n              {navLinks.map((link) => (\n                <Link\n                  key={link.href}\n                  href={link.href}\n                  className={`${\n                    transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                  } transition-colors`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {link.label}\n                </Link>\n              ))}\n              {externalLinks.map((link) => (\n                <a\n                  key={link.href}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className={`${\n                    transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                  } transition-colors`}\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {link.label}\n                </a>\n              ))}\n              \n              {showAuthButtons && (\n                <div className=\"pt-4 border-t border-gray-200 space-y-4\">\n                  {user ? (\n                    <>\n                      <Link\n                        href=\"/dashboard\"\n                        className={`flex items-center space-x-2 ${\n                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                        } transition-colors`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        <User className=\"w-4 h-4\" />\n                        <span>Dashboard</span>\n                      </Link>\n                      <button\n                        onClick={() => {\n                          handleLogout();\n                          setMobileMenuOpen(false);\n                        }}\n                        className={`flex items-center space-x-2 ${\n                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                        } transition-colors`}\n                      >\n                        <LogOut className=\"w-4 h-4\" />\n                        <span>Logout</span>\n                      </button>\n                    </>\n                  ) : (\n                    <>\n                      <Link\n                        href=\"/auth/login\"\n                        className={`block ${\n                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'\n                        } transition-colors`}\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        Sign In\n                      </Link>\n                      <Link\n                        href=\"/auth/register\"\n                        className=\"block bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-center hover:shadow-lg transition-all duration-300 web3-glow\"\n                        onClick={() => setMobileMenuOpen(false)}\n                      >\n                        Get Started\n                      </Link>\n                    </>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAce,SAAS,WAAW,EACjC,kBAAkB,IAAI,EACtB,cAAc,KAAK,EACnB,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAgB,OAAO;QAAc;QAC7C;YAAE,MAAM;YAAa,OAAO;QAAoB;QAChD;YAAE,MAAM;YAAU,OAAO;QAAO;QAChC;YAAE,MAAM;YAAc,OAAO;QAAY;QACzC;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAS,OAAO;QAAO;KAChC;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAyB,OAAO;QAAY;QACpD;YAAE,MAAM;YAAyB,OAAO;QAAgB;KACzD;IAED,MAAM,cAAc,cAChB,sDACA;IAEJ,qBACE,8OAAC;QAAO,WAAW,GAAG,YAAY,CAAC,EAAE,WAAW;kBAC9C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,GACT,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;kDAEnB,KAAK,KAAK;uCANN,KAAK,IAAI;;;;;gCASjB,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,QAAO;wCACP,KAAI;wCACJ,WAAW,GACT,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;kDAEnB,KAAK,KAAK;uCARN,KAAK,IAAI;;;;;;;;;;;sCAcpB,8OAAC;4BAAI,WAAU;;gCACZ,iCACC;8CACG,qBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,4BAA4B,EACtC,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;;kEAEpB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAErC,8OAAC;gDACC,SAAS;gDACT,WAAW,CAAC,4BAA4B,EACtC,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;;kEAEpB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;6DAIvC;;0DACE,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,GACT,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;0DACrB;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;8CAST,8OAAC;oCACC,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAW,CAAC,UAAU,EACpB,cAAc,eAAe,iBAC7B;8CAED,+BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,gCACC,8OAAC;oBAAI,WAAW,CAAC,wBAAwB,EACvC,cAAc,iDAAiD,mBAC/D;8BACA,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,GACT,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;oCACpB,SAAS,IAAM,kBAAkB;8CAEhC,KAAK,KAAK;mCAPN,KAAK,IAAI;;;;;4BAUjB,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,QAAO;oCACP,KAAI;oCACJ,WAAW,GACT,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;oCACpB,SAAS,IAAM,kBAAkB;8CAEhC,KAAK,KAAK;mCATN,KAAK,IAAI;;;;;4BAajB,iCACC,8OAAC;gCAAI,WAAU;0CACZ,qBACC;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAW,CAAC,4BAA4B,EACtC,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;4CACpB,SAAS,IAAM,kBAAkB;;8DAEjC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CACC,SAAS;gDACP;gDACA,kBAAkB;4CACpB;4CACA,WAAW,CAAC,4BAA4B,EACtC,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;;8DAEpB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;iEAIV;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAW,CAAC,MAAM,EAChB,cAAc,mCAAmC,oCAClD,kBAAkB,CAAC;4CACpB,SAAS,IAAM,kBAAkB;sDAClC;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/tokenize/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport Navigation from '@/components/layout/Navigation';\nimport {\n  Building2,\n  ArrowRight,\n  Shield,\n  DollarSign,\n  Users,\n  Zap,\n  CheckCircle,\n  AlertTriangle,\n  FileText,\n  Clock,\n  ExternalLink\n} from 'lucide-react';\n\nexport default function TokenizePage() {\n  const router = useRouter();\n  const { user } = useAuth();\n\n  const handleStartTokenization = () => {\n    if (!user) {\n      router.push('/auth/login?redirect=/tokenize');\n      return;\n    }\n    router.push('/tokenize/llc-setup');\n  };\n\n  const steps = [\n    {\n      number: 1,\n      title: 'Form Your LLC',\n      description: 'Create a Limited Liability Company to legally own your property',\n      icon: Building2,\n      color: 'blue',\n      estimated: '3-5 days'\n    },\n    {\n      number: 2,\n      title: 'Transfer Property',\n      description: 'Transfer property ownership from personal name to your LLC',\n      icon: FileText,\n      color: 'green',\n      estimated: '1-2 weeks'\n    },\n    {\n      number: 3,\n      title: 'Property Verification',\n      description: 'Verify property details, ownership, and compliance requirements',\n      icon: Shield,\n      color: 'purple',\n      estimated: '2-3 days'\n    },\n    {\n      number: 4,\n      title: 'Create NFT',\n      description: 'Mint your property as an NFT on the blockchain',\n      icon: Zap,\n      color: 'orange',\n      estimated: '1 day'\n    },\n    {\n      number: 5,\n      title: 'List & Trade',\n      description: 'List your tokenized property on the marketplace',\n      icon: Users,\n      color: 'indigo',\n      estimated: 'Immediate'\n    }\n  ];\n\n  const benefits = [\n    {\n      icon: Shield,\n      title: 'Legal Compliance',\n      description: 'Full regulatory compliance through proper LLC structure'\n    },\n    {\n      icon: DollarSign,\n      title: 'Fractional Ownership',\n      description: 'Enable multiple investors to own shares of your property'\n    },\n    {\n      icon: Users,\n      title: 'Liquidity',\n      description: 'Trade property shares instantly on the blockchain'\n    },\n    {\n      icon: Zap,\n      title: 'Transparency',\n      description: 'All transactions recorded immutably on the blockchain'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Tokenize Your Real Estate\n            </h1>\n            <p className=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n              Transform your property into a digital asset. Create an LLC, transfer ownership, \n              and mint your property as an NFT for fractional investment opportunities.\n            </p>\n            <button\n              onClick={handleStartTokenization}\n              className=\"bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors flex items-center mx-auto\"\n            >\n              Start Tokenization Process\n              <ArrowRight className=\"w-5 h-5 ml-2\" />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        {/* Why Tokenize Section */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Why Tokenize Your Property?</h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-12\">\n            Property tokenization unlocks new opportunities for real estate investment, \n            providing liquidity, transparency, and accessibility like never before.\n          </p>\n          \n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {benefits.map((benefit, index) => {\n              const Icon = benefit.icon;\n              return (\n                <div key={index} className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <Icon className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{benefit.title}</h3>\n                  <p className=\"text-gray-600\">{benefit.description}</p>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Process Steps */}\n        <div className=\"mb-16\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Tokenization Process</h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Follow our step-by-step process to legally and securely tokenize your real estate property.\n            </p>\n          </div>\n\n          <div className=\"space-y-8\">\n            {steps.map((step, index) => {\n              const Icon = step.icon;\n              const isLast = index === steps.length - 1;\n              \n              return (\n                <div key={step.number} className=\"relative\">\n                  <div className=\"flex items-start space-x-6\">\n                    <div className=\"flex-shrink-0\">\n                      <div className={`w-12 h-12 bg-${step.color}-100 rounded-full flex items-center justify-center`}>\n                        <Icon className={`w-6 h-6 text-${step.color}-600`} />\n                      </div>\n                    </div>\n                    <div className=\"flex-1 bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h3 className=\"text-xl font-semibold text-gray-900\">\n                          Step {step.number}: {step.title}\n                        </h3>\n                        <div className=\"flex items-center text-sm text-gray-500\">\n                          <Clock className=\"w-4 h-4 mr-1\" />\n                          {step.estimated}\n                        </div>\n                      </div>\n                      <p className=\"text-gray-600\">{step.description}</p>\n                    </div>\n                  </div>\n                  \n                  {!isLast && (\n                    <div className=\"absolute left-6 top-12 w-0.5 h-8 bg-gray-300\"></div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Requirements Section */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-16\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Requirements & Prerequisites</h2>\n          \n          <div className=\"grid md:grid-cols-2 gap-8\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <CheckCircle className=\"w-5 h-5 text-green-600 mr-2\" />\n                What You Need\n              </h3>\n              <ul className=\"space-y-3 text-gray-600\">\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 mr-2 mt-0.5\" />\n                  Property ownership or purchase agreement\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 mr-2 mt-0.5\" />\n                  Property appraisal or market valuation\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 mr-2 mt-0.5\" />\n                  Valid government-issued ID\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 mr-2 mt-0.5\" />\n                  Business address for LLC registration\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 mr-2 mt-0.5\" />\n                  Cryptocurrency wallet (MetaMask recommended)\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                <AlertTriangle className=\"w-5 h-5 text-yellow-600 mr-2\" />\n                Important Considerations\n              </h3>\n              <ul className=\"space-y-3 text-gray-600\">\n                <li className=\"flex items-start\">\n                  <AlertTriangle className=\"w-4 h-4 text-yellow-500 mr-2 mt-0.5\" />\n                  LLC formation costs vary by state ($75-$500)\n                </li>\n                <li className=\"flex items-start\">\n                  <AlertTriangle className=\"w-4 h-4 text-yellow-500 mr-2 mt-0.5\" />\n                  Property transfer may trigger tax implications\n                </li>\n                <li className=\"flex items-start\">\n                  <AlertTriangle className=\"w-4 h-4 text-yellow-500 mr-2 mt-0.5\" />\n                  Existing mortgages may need lender approval\n                </li>\n                <li className=\"flex items-start\">\n                  <AlertTriangle className=\"w-4 h-4 text-yellow-500 mr-2 mt-0.5\" />\n                  Compliance with local real estate regulations\n                </li>\n                <li className=\"flex items-start\">\n                  <AlertTriangle className=\"w-4 h-4 text-yellow-500 mr-2 mt-0.5\" />\n                  Blockchain transaction fees apply\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl text-white p-8\">\n            <h2 className=\"text-2xl font-bold mb-4\">Ready to Get Started?</h2>\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n              Join the future of real estate investment. Our guided process makes tokenization \n              simple, legal, and secure.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button\n                onClick={handleStartTokenization}\n                className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center\"\n              >\n                Begin LLC Formation\n                <ArrowRight className=\"w-4 h-4 ml-2\" />\n              </button>\n              <button\n                onClick={() => router.push('/docs')}\n                className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center\"\n              >\n                Learn More\n                <ExternalLink className=\"w-4 h-4 ml-2\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAmBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,0BAA0B;QAC9B,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO;YACP,WAAW;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,WAAW;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,WAAW;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,WAAW;QACb;QACA;YACE,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACb;KACD;IAED,MAAM,WAAW;QACf;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCACC,SAAS;gCACT,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAK7D,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oCACtB,MAAM,OAAO,QAAQ,IAAI;oCACzB,qBACE,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DAA4C,QAAQ,KAAK;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCALzC;;;;;gCAQd;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAKzD,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,SAAS,UAAU,MAAM,MAAM,GAAG;oCAExC,qBACE,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,kDAAkD,CAAC;sEAC5F,cAAA,8OAAC;gEAAK,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;kEAGrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;;4EAAsC;4EAC5C,KAAK,MAAM;4EAAC;4EAAG,KAAK,KAAK;;;;;;;kFAEjC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,KAAK,SAAS;;;;;;;;;;;;;0EAGnB,8OAAC;gEAAE,WAAU;0EAAiB,KAAK,WAAW;;;;;;;;;;;;;;;;;;4CAIjD,CAAC,wBACA,8OAAC;gDAAI,WAAU;;;;;;;uCAtBT,KAAK,MAAM;;;;;gCA0BzB;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;0DAGzD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAuC;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAuC;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAuC;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAuC;;;;;;;kEAGhE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAuC;;;;;;;;;;;;;;;;;;;kDAMpE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;0DAG5D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAwC;;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAwC;;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAwC;;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAwC;;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;;gDACX;8DAEC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;gDACX;8DAEC,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC", "debugId": null}}]}