'use client';

import { useState } from 'react';
import Link from 'next/link';
import Navigation from '@/components/layout/Navigation';
import {
  ArrowLeft,
  Building2,
  Users,
  Calendar,
  MessageSquare,
  TrendingUp,
  Award,
  Zap
} from 'lucide-react';
import CommunityEvents from '@/components/community/CommunityEvents';
import CommunityPosts from '@/components/community/CommunityPosts';
import { useCommunityStats } from '@/hooks/useCommunity';

export default function CommunityPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'posts'>('overview');
  const { stats, loading: statsLoading } = useCommunityStats();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Welcome to Our Community
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Connect with fellow real estate enthusiasts, share knowledge, 
            attend events, and grow together in the ManageLife ecosystem.
          </p>
          
          {/* Community Stats */}
          {!statsLoading && stats && (
            <div className="grid md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                <Users className="w-8 h-8 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.activeMembers}</p>
                <p className="text-sm text-blue-100">Active Members</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                <Calendar className="w-8 h-8 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.totalEvents}</p>
                <p className="text-sm text-blue-100">Events Hosted</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                <MessageSquare className="w-8 h-8 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.totalPosts}</p>
                <p className="text-sm text-blue-100">Community Posts</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                <TrendingUp className="w-8 h-8 mx-auto mb-2" />
                <p className="text-2xl font-bold">{stats.totalParticipants}</p>
                <p className="text-sm text-blue-100">Event Participants</p>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Features Overview */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Community Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover all the ways you can engage with our vibrant community
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Events & Workshops</h3>
              <p className="text-gray-600">
                Join webinars, workshops, and networking events to learn from experts and connect with peers.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageSquare className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Discussion Forums</h3>
              <p className="text-gray-600">
                Share insights, ask questions, and participate in meaningful discussions about real estate.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Rewards & Recognition</h3>
              <p className="text-gray-600">
                Earn $MLIFE tokens for active participation and unlock exclusive community benefits.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100 rounded-lg p-1 flex">
              <button
                onClick={() => setActiveTab('overview')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'overview'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <TrendingUp className="w-4 h-4 inline mr-2" />
                Overview
              </button>
              <button
                onClick={() => setActiveTab('events')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'events'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Events
              </button>
              <button
                onClick={() => setActiveTab('posts')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'posts'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <MessageSquare className="w-4 h-4 inline mr-2" />
                Discussions
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="max-w-6xl mx-auto">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Welcome Section */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                  <Zap className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Join Our Growing Community
                  </h3>
                  <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                    Connect with real estate professionals, investors, and enthusiasts. 
                    Share knowledge, learn from experts, and grow your network in our supportive community.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                      href="/auth/register"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow"
                    >
                      Join Community
                    </Link>
                    <button
                      onClick={() => setActiveTab('events')}
                      className="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
                    >
                      Browse Events
                    </button>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="grid md:grid-cols-2 gap-8">
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h4>
                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="w-4 h-4 mr-2 text-blue-600" />
                        <span>{stats?.upcomingEvents || 0} upcoming events this month</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MessageSquare className="w-4 h-4 mr-2 text-purple-600" />
                        <span>{stats?.totalPosts || 0} community discussions</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="w-4 h-4 mr-2 text-green-600" />
                        <span>{stats?.activeMembers || 0} active community members</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Community Benefits</h4>
                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <Award className="w-4 h-4 mr-2 text-yellow-600" />
                        <span>Earn $MLIFE tokens for participation</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Zap className="w-4 h-4 mr-2 text-blue-600" />
                        <span>Access to exclusive events and content</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <TrendingUp className="w-4 h-4 mr-2 text-green-600" />
                        <span>Network with industry professionals</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'events' && <CommunityEvents />}
            {activeTab === 'posts' && <CommunityPosts />}
          </div>
        </div>
      </section>
    </div>
  );
}
