'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAccount, useChainId } from 'wagmi';
import Navigation from '@/components/layout/Navigation';
import {
  ArrowLeft,
  Building2,
  Coins,
  Wallet,
  Shield,
  TrendingUp,
  Zap,
  Globe
} from 'lucide-react';
import WalletConnect from '@/components/web3/WalletConnect';
import MLifeWallet from '@/components/web3/MLifeWallet';
import PropertyTokenizer from '@/components/web3/PropertyTokenizer';
import { CHAIN_CONFIG } from '@/lib/web3';

// Mock property for tokenization demo
const mockProperty = {
  id: '1',
  ownerId: 'owner1',
  title: 'Modern Downtown Apartment',
  description: 'Beautiful 2-bedroom apartment in the heart of downtown with stunning city views',
  address: '123 Main St, Apt 15B',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  country: 'USA',
  propertyType: 'apartment' as const,
  price: 450000,
  currency: 'USD' as const,
  bedrooms: 2,
  bathrooms: 2,
  squareFeet: 1200,
  images: ['/api/placeholder/400/300'],
  amenities: ['Air Conditioning', 'Elevator', 'Fitness Center'],
  isTokenized: false,
  nftTokenId: undefined,
  nftContractAddress: undefined,
  status: 'available' as const,
  createdAt: new Date('2025-01-15'),
  updatedAt: new Date('2025-01-20'),
};

export default function Web3Page() {
  const { isConnected } = useAccount();
  const chainId = useChainId();
  const [activeTab, setActiveTab] = useState<'wallet' | 'tokens' | 'nft'>('wallet');

  const currentChain = CHAIN_CONFIG[chainId as keyof typeof CHAIN_CONFIG];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Web3 Features
          </h1>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Experience the power of blockchain technology with real estate tokenization, 
            $MLIFE tokens, and decentralized property management.
          </p>
          
          {/* Chain Info */}
          {isConnected && currentChain && (
            <div className="inline-flex items-center bg-white/20 rounded-lg px-4 py-2 backdrop-blur-sm">
              <Globe className="w-5 h-5 mr-2" />
              <span>Connected to {currentChain.name}</span>
              {currentChain.isTestnet && (
                <span className="ml-2 text-xs bg-yellow-500 text-yellow-900 px-2 py-1 rounded">
                  Testnet
                </span>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Features Overview */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wallet className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Wallet Integration</h3>
              <p className="text-gray-600">
                Connect your MetaMask wallet and manage your digital assets securely on the blockchain.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Coins className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">$MLIFE Tokens</h3>
              <p className="text-gray-600">
                Earn and spend $MLIFE tokens for platform activities, rewards, and governance participation.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">NFT Properties</h3>
              <p className="text-gray-600">
                Tokenize real estate properties as NFTs for transparent ownership and easy trading.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo */}
      <section className="py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Try Web3 Features
            </h2>
            <p className="text-xl text-gray-600">
              Connect your wallet and explore blockchain functionality
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-100 rounded-lg p-1 flex">
              <button
                onClick={() => setActiveTab('wallet')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'wallet'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Wallet className="w-4 h-4 inline mr-2" />
                Wallet
              </button>
              <button
                onClick={() => setActiveTab('tokens')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'tokens'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Coins className="w-4 h-4 inline mr-2" />
                $MLIFE
              </button>
              <button
                onClick={() => setActiveTab('nft')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'nft'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Shield className="w-4 h-4 inline mr-2" />
                NFT
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="max-w-4xl mx-auto">
            {activeTab === 'wallet' && (
              <div className="grid md:grid-cols-2 gap-8">
                <WalletConnect />
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Blockchain Benefits
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <Shield className="w-5 h-5 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-gray-900">Security</h4>
                        <p className="text-sm text-gray-600">
                          Your assets are secured by blockchain cryptography
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <TrendingUp className="w-5 h-5 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-gray-900">Transparency</h4>
                        <p className="text-sm text-gray-600">
                          All transactions are publicly verifiable
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Zap className="w-5 h-5 text-purple-600 mr-3 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-gray-900">Efficiency</h4>
                        <p className="text-sm text-gray-600">
                          Automated smart contracts reduce costs
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'tokens' && (
              <div className="grid md:grid-cols-1 gap-8">
                <MLifeWallet />
              </div>
            )}

            {activeTab === 'nft' && (
              <div className="grid md:grid-cols-1 gap-8">
                <PropertyTokenizer 
                  property={mockProperty}
                  onTokenized={(tokenId, txHash) => {
                    console.log('Property tokenized:', { tokenId, txHash });
                    // In a real app, you would update the property in the database
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join the future of real estate with blockchain technology. 
            Create your account and start exploring Web3 features today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/register"
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Create Account
            </Link>
            <Link
              href="/marketplace"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              Explore Properties
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
