{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/whitepaper/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { \n  ArrowLeft, \n  Building2, \n  Download,\n  FileText,\n  ExternalLink,\n  Coins,\n  Shield,\n  TrendingUp,\n  Users,\n  Zap,\n  Globe\n} from 'lucide-react';\n\nexport default function WhitepaperPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Home\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n              </div>\n            </div>\n            <Link\n              href=\"/auth/register\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow\"\n            >\n              Get Started\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <FileText className=\"w-16 h-16 mx-auto mb-6\" />\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            ManageLife Whitepaper\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            A comprehensive technical overview of the ManageLife platform, \n            tokenomics, and the future of real estate tokenization.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center\">\n              <Download className=\"w-5 h-5 mr-2\" />\n              Download PDF\n            </button>\n            <button className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center\">\n              <ExternalLink className=\"w-5 h-5 mr-2\" />\n              View Online\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Table of Contents */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 text-center\">Table of Contents</h2>\n            \n            <div className=\"bg-gray-50 rounded-xl p-8\">\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div className=\"space-y-3\">\n                  <h3 className=\"font-semibold text-gray-900 mb-4\">Part I: Foundation</h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <div className=\"flex justify-between\">\n                      <span>1. Executive Summary</span>\n                      <span>Page 3</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>2. Problem Statement</span>\n                      <span>Page 5</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>3. Solution Overview</span>\n                      <span>Page 8</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>4. Market Analysis</span>\n                      <span>Page 12</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <h3 className=\"font-semibold text-gray-900 mb-4\">Part II: Technology</h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <div className=\"flex justify-between\">\n                      <span>5. Platform Architecture</span>\n                      <span>Page 16</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>6. Blockchain Integration</span>\n                      <span>Page 20</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>7. Smart Contracts</span>\n                      <span>Page 24</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>8. Security Framework</span>\n                      <span>Page 28</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <h3 className=\"font-semibold text-gray-900 mb-4\">Part III: Tokenomics</h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <div className=\"flex justify-between\">\n                      <span>9. $MLIFE Token</span>\n                      <span>Page 32</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>10. NFTi & NFTr System</span>\n                      <span>Page 36</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>11. Reward Mechanisms</span>\n                      <span>Page 40</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>12. Economic Model</span>\n                      <span>Page 44</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"space-y-3\">\n                  <h3 className=\"font-semibold text-gray-900 mb-4\">Part IV: Implementation</h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <div className=\"flex justify-between\">\n                      <span>13. Roadmap</span>\n                      <span>Page 48</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>14. Team & Advisors</span>\n                      <span>Page 52</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>15. Legal Framework</span>\n                      <span>Page 56</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>16. Risk Assessment</span>\n                      <span>Page 60</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Key Highlights */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Key Highlights</h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Essential insights from the ManageLife whitepaper\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\">\n                <Coins className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Dual Token System</h3>\n              <p className=\"text-gray-600\">\n                Innovative NFTi (property ownership) and NFTr (rental rights) tokens \n                that separate ownership from usage rights, creating new investment opportunities.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4\">\n                <TrendingUp className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">$MLIFE Economy</h3>\n              <p className=\"text-gray-600\">\n                Comprehensive reward system that incentivizes positive behavior \n                across all platform participants, creating a sustainable token economy.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4\">\n                <Shield className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Security First</h3>\n              <p className=\"text-gray-600\">\n                Multi-layered security architecture with smart contract audits, \n                formal verification, and comprehensive risk management protocols.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4\">\n                <Users className=\"w-6 h-6 text-orange-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Multi-Role Platform</h3>\n              <p className=\"text-gray-600\">\n                Designed for five distinct user roles with tailored features \n                and incentives for homeowners, renters, buyers, portfolio managers, and community members.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4\">\n                <Zap className=\"w-6 h-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Instant Settlements</h3>\n              <p className=\"text-gray-600\">\n                Smart contract automation enables instant property transfers, \n                rent payments, and reward distributions without intermediaries.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4\">\n                <Globe className=\"w-6 h-6 text-indigo-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Global Accessibility</h3>\n              <p className=\"text-gray-600\">\n                Cross-border real estate investment made possible through \n                blockchain technology and regulatory compliance frameworks.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Executive Summary Preview */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8 text-center\">Executive Summary</h2>\n            \n            <div className=\"prose prose-lg max-w-none\">\n              <p className=\"text-gray-600 leading-relaxed mb-6\">\n                ManageLife represents a paradigm shift in real estate investment and management, \n                leveraging blockchain technology to create a transparent, efficient, and accessible \n                ecosystem for property stakeholders worldwide.\n              </p>\n              \n              <p className=\"text-gray-600 leading-relaxed mb-6\">\n                Our platform addresses critical inefficiencies in traditional real estate markets \n                through innovative tokenization mechanisms, automated smart contracts, and a \n                comprehensive reward system that aligns incentives across all participants.\n              </p>\n              \n              <p className=\"text-gray-600 leading-relaxed mb-6\">\n                The dual token architecture (NFTi for ownership, NFTr for rental rights) enables \n                unprecedented flexibility in property investment strategies, while the $MLIFE token \n                creates a sustainable economy that rewards positive behavior and platform engagement.\n              </p>\n              \n              <div className=\"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\">\n                <p className=\"text-blue-800 font-medium\">\n                  \"ManageLife is not just a platform; it's a complete reimagining of how real estate \n                  can work in the digital age, making property investment accessible to everyone while \n                  maintaining the highest standards of security and transparency.\"\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Download CTA */}\n      <section className=\"py-16 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-6\">\n            Ready to Dive Deeper?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Download the complete whitepaper to explore the technical details, \n            tokenomics, and roadmap for the future of real estate.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center\">\n              <Download className=\"w-5 h-5 mr-2\" />\n              Download Full Whitepaper\n            </button>\n            <Link\n              href=\"/docs\"\n              className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center\"\n            >\n              Explore Documentation\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAce,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;0CAGtD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAElE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAElE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYnD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}