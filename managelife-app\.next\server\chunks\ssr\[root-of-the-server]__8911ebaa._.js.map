{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, UserRole } from '@/types';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  loginWithWallet: (walletAddress: string) => Promise<{ isNewUser: boolean }>;\n  register: (userData: {\n    name: string;\n    email: string;\n    password: string;\n    confirmPassword: string;\n    roles: UserRole[];\n  }) => Promise<void>;\n  logout: () => Promise<void>;\n  updateUser: (userData: Partial<User>) => Promise<void>;\n  updateProfile: (profileData: any) => Promise<void>;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if user is authenticated on mount\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/auth/me');\n      if (response.ok) {\n        const data = await response.json();\n        setUser({\n          ...data.user,\n          joinedAt: new Date(data.user.joinedAt),\n        });\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email: string, password: string) => {\n    const response = await fetch('/api/auth/login', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ email, password }),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Login failed');\n    }\n\n    setUser({\n      ...data.user,\n      joinedAt: new Date(data.user.joinedAt),\n    });\n  };\n\n  const loginWithWallet = async (walletAddress: string): Promise<{ isNewUser: boolean }> => {\n    const response = await fetch('/api/auth/wallet', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ walletAddress }),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Wallet login failed');\n    }\n\n    setUser({\n      ...data.user,\n      joinedAt: new Date(data.user.joinedAt),\n    });\n\n    return { isNewUser: data.isNewUser };\n  };\n\n  const register = async (userData: {\n    name: string;\n    email: string;\n    password: string;\n    confirmPassword: string;\n    roles: UserRole[];\n  }) => {\n    const response = await fetch('/api/auth/register', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(userData),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Registration failed');\n    }\n\n    setUser({\n      ...data.user,\n      joinedAt: new Date(data.user.joinedAt),\n    });\n  };\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n    }\n  };\n\n  const updateUser = async (userData: Partial<User>) => {\n    const response = await fetch('/api/auth/me', {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(userData),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new Error(data.error || 'Update failed');\n    }\n\n    setUser({\n      ...data.user,\n      joinedAt: new Date(data.user.joinedAt),\n    });\n  };\n\n  const refreshUser = async () => {\n    await checkAuth();\n  };\n\n  const value = {\n    user,\n    loading,\n    login,\n    loginWithWallet,\n    register,\n    logout,\n    updateUser,\n    updateProfile: updateUser, // Alias for updateUser\n    refreshUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ;oBACN,GAAG,KAAK,IAAI;oBACZ,UAAU,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,MAAM,WAAW,MAAM,MAAM,mBAAmB;YAC9C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;YAAS;QACzC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,QAAQ;YACN,GAAG,KAAK,IAAI;YACZ,UAAU,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ;QACvC;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,WAAW,MAAM,MAAM,oBAAoB;YAC/C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAc;QACvC;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,QAAQ;YACN,GAAG,KAAK,IAAI;YACZ,UAAU,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ;QACvC;QAEA,OAAO;YAAE,WAAW,KAAK,SAAS;QAAC;IACrC;IAEA,MAAM,WAAW,OAAO;QAOtB,MAAM,WAAW,MAAM,MAAM,sBAAsB;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,QAAQ;YACN,GAAG,KAAK,IAAI;YACZ,UAAU,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ;QACvC;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,WAAW,MAAM,MAAM,gBAAgB;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,QAAQ;YACN,GAAG,KAAK,IAAI;YACZ,UAAU,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ;QACvC;IACF;IAEA,MAAM,cAAc;QAClB,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA,eAAe;QACf;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/lib/web3.ts"], "sourcesContent": ["import { createConfig, http } from 'wagmi';\nimport { mainnet, sepolia, polygon, polygonMumbai } from 'wagmi/chains';\nimport { getDefaultConfig } from '@rainbow-me/rainbowkit';\n\n// Project configuration\nconst projectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'demo-project-id';\n\n// Wagmi configuration\nexport const config = getDefaultConfig({\n  appName: 'ManageLife',\n  projectId,\n  chains: [mainnet, sepolia, polygon, polygonMumbai],\n  ssr: true,\n});\n\n// Contract addresses (these would be real deployed contract addresses)\nexport const CONTRACT_ADDRESSES = {\n  // Sepolia testnet addresses (for development)\n  SEPOLIA: {\n    NFTi: '******************************************', // Property NFT contract\n    NFTr: '******************************************', // Rental NFT contract\n    MLIFE_TOKEN: '******************************************', // $MLIFE ERC20 token\n    MARKETPLACE: '******************************************', // Marketplace contract\n    PROPERTY_REGISTRY: '******************************************', // Property registry\n  },\n  // Polygon mainnet addresses (for production)\n  POLYGON: {\n    NFTi: '******************************************',\n    NFTr: '******************************************',\n    MLIFE_TOKEN: '******************************************',\n    MARKETPLACE: '******************************************',\n    PROPERTY_REGISTRY: '******************************************',\n  },\n} as const;\n\n// Contract ABIs (simplified for demo - in production these would be full ABIs)\nexport const CONTRACT_ABIS = {\n  // ERC721 NFT ABI (simplified)\n  NFT: [\n    'function mint(address to, uint256 tokenId, string memory tokenURI) public',\n    'function ownerOf(uint256 tokenId) public view returns (address)',\n    'function tokenURI(uint256 tokenId) public view returns (string)',\n    'function approve(address to, uint256 tokenId) public',\n    'function transferFrom(address from, address to, uint256 tokenId) public',\n    'function balanceOf(address owner) public view returns (uint256)',\n    'event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)',\n  ],\n  \n  // ERC20 Token ABI (simplified)\n  ERC20: [\n    'function balanceOf(address account) public view returns (uint256)',\n    'function transfer(address to, uint256 amount) public returns (bool)',\n    'function approve(address spender, uint256 amount) public returns (bool)',\n    'function allowance(address owner, address spender) public view returns (uint256)',\n    'function totalSupply() public view returns (uint256)',\n    'function decimals() public view returns (uint8)',\n    'function symbol() public view returns (string)',\n    'function name() public view returns (string)',\n    'event Transfer(address indexed from, address indexed to, uint256 value)',\n    'event Approval(address indexed owner, address indexed spender, uint256 value)',\n  ],\n  \n  // Marketplace ABI (simplified)\n  MARKETPLACE: [\n    'function listProperty(uint256 tokenId, uint256 price, bool isForRent) public',\n    'function buyProperty(uint256 listingId) public payable',\n    'function rentProperty(uint256 listingId, uint256 duration) public payable',\n    'function cancelListing(uint256 listingId) public',\n    'function getListing(uint256 listingId) public view returns (tuple(uint256 tokenId, address seller, uint256 price, bool isForRent, bool isActive))',\n    'event PropertyListed(uint256 indexed listingId, uint256 indexed tokenId, address indexed seller, uint256 price, bool isForRent)',\n    'event PropertySold(uint256 indexed listingId, address indexed buyer)',\n    'event PropertyRented(uint256 indexed listingId, address indexed renter, uint256 duration)',\n  ],\n  \n  // Property Registry ABI (simplified)\n  PROPERTY_REGISTRY: [\n    'function registerProperty(string memory propertyData, string memory location) public returns (uint256)',\n    'function getProperty(uint256 propertyId) public view returns (tuple(string propertyData, string location, address owner, bool isTokenized))',\n    'function tokenizeProperty(uint256 propertyId, string memory tokenURI) public returns (uint256)',\n    'function updateProperty(uint256 propertyId, string memory propertyData) public',\n    'event PropertyRegistered(uint256 indexed propertyId, address indexed owner)',\n    'event PropertyTokenized(uint256 indexed propertyId, uint256 indexed tokenId)',\n  ],\n} as const;\n\n// Helper function to get contract address for current chain\nexport function getContractAddress(contractName: keyof typeof CONTRACT_ADDRESSES.SEPOLIA, chainId: number) {\n  switch (chainId) {\n    case sepolia.id:\n      return CONTRACT_ADDRESSES.SEPOLIA[contractName];\n    case polygon.id:\n      return CONTRACT_ADDRESSES.POLYGON[contractName];\n    default:\n      return CONTRACT_ADDRESSES.SEPOLIA[contractName]; // Default to Sepolia for development\n  }\n}\n\n// Supported chains for the application\nexport const SUPPORTED_CHAINS = [sepolia, polygon, polygonMumbai];\n\n// Chain-specific configuration\nexport const CHAIN_CONFIG = {\n  [sepolia.id]: {\n    name: 'Sepolia Testnet',\n    currency: 'ETH',\n    blockExplorer: 'https://sepolia.etherscan.io',\n    isTestnet: true,\n  },\n  [polygon.id]: {\n    name: 'Polygon',\n    currency: 'MATIC',\n    blockExplorer: 'https://polygonscan.com',\n    isTestnet: false,\n  },\n  [polygonMumbai.id]: {\n    name: 'Polygon Mumbai',\n    currency: 'MATIC',\n    blockExplorer: 'https://mumbai.polygonscan.com',\n    isTestnet: true,\n  },\n} as const;\n\n// Gas estimation helpers\nexport const GAS_LIMITS = {\n  MINT_NFT: 200000,\n  TRANSFER_NFT: 100000,\n  LIST_PROPERTY: 150000,\n  BUY_PROPERTY: 200000,\n  RENT_PROPERTY: 180000,\n  TOKEN_TRANSFER: 65000,\n  TOKEN_APPROVE: 50000,\n} as const;\n\n// Transaction types for tracking\nexport type TransactionType = \n  | 'mint_nft'\n  | 'transfer_nft'\n  | 'list_property'\n  | 'buy_property'\n  | 'rent_property'\n  | 'token_transfer'\n  | 'token_approve';\n\n// Transaction status\nexport type TransactionStatus = 'pending' | 'confirmed' | 'failed';\n\n// Transaction record interface\nexport interface TransactionRecord {\n  hash: string;\n  type: TransactionType;\n  status: TransactionStatus;\n  timestamp: number;\n  chainId: number;\n  from: string;\n  to?: string;\n  value?: string;\n  gasUsed?: string;\n  blockNumber?: number;\n}\n\n// Error types\nexport class Web3Error extends Error {\n  constructor(\n    message: string,\n    public code?: string,\n    public data?: any\n  ) {\n    super(message);\n    this.name = 'Web3Error';\n  }\n}\n\n// Helper function to format token amounts\nexport function formatTokenAmount(amount: bigint, decimals: number = 18): string {\n  const divisor = BigInt(10 ** decimals);\n  const quotient = amount / divisor;\n  const remainder = amount % divisor;\n  \n  if (remainder === 0n) {\n    return quotient.toString();\n  }\n  \n  const remainderStr = remainder.toString().padStart(decimals, '0');\n  const trimmedRemainder = remainderStr.replace(/0+$/, '');\n  \n  if (trimmedRemainder === '') {\n    return quotient.toString();\n  }\n  \n  return `${quotient}.${trimmedRemainder}`;\n}\n\n// Helper function to parse token amounts\nexport function parseTokenAmount(amount: string, decimals: number = 18): bigint {\n  const [whole, fraction = ''] = amount.split('.');\n  const paddedFraction = fraction.padEnd(decimals, '0').slice(0, decimals);\n  return BigInt(whole + paddedFraction);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAAA;AAAA;AAAA;AACA;;;AAEA,wBAAwB;AACxB,MAAM,YAAY,QAAQ,GAAG,CAAC,qCAAqC,IAAI;AAGhE,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE;IACrC,SAAS;IACT;IACA,QAAQ;QAAC,gKAAA,CAAA,UAAO;QAAE,gKAAA,CAAA,UAAO;QAAE,gKAAA,CAAA,UAAO;QAAE,sKAAA,CAAA,gBAAa;KAAC;IAClD,KAAK;AACP;AAGO,MAAM,qBAAqB;IAChC,8CAA8C;IAC9C,SAAS;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;QACb,mBAAmB;IACrB;IACA,6CAA6C;IAC7C,SAAS;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,aAAa;QACb,mBAAmB;IACrB;AACF;AAGO,MAAM,gBAAgB;IAC3B,8BAA8B;IAC9B,KAAK;QACH;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,+BAA+B;IAC/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,+BAA+B;IAC/B,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qCAAqC;IACrC,mBAAmB;QACjB;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,SAAS,mBAAmB,YAAqD,EAAE,OAAe;IACvG,OAAQ;QACN,KAAK,gKAAA,CAAA,UAAO,CAAC,EAAE;YACb,OAAO,mBAAmB,OAAO,CAAC,aAAa;QACjD,KAAK,gKAAA,CAAA,UAAO,CAAC,EAAE;YACb,OAAO,mBAAmB,OAAO,CAAC,aAAa;QACjD;YACE,OAAO,mBAAmB,OAAO,CAAC,aAAa,EAAE,qCAAqC;IAC1F;AACF;AAGO,MAAM,mBAAmB;IAAC,gKAAA,CAAA,UAAO;IAAE,gKAAA,CAAA,UAAO;IAAE,sKAAA,CAAA,gBAAa;CAAC;AAG1D,MAAM,eAAe;IAC1B,CAAC,gKAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;IACb;IACA,CAAC,gKAAA,CAAA,UAAO,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;IACb;IACA,CAAC,sKAAA,CAAA,gBAAa,CAAC,EAAE,CAAC,EAAE;QAClB,MAAM;QACN,UAAU;QACV,eAAe;QACf,WAAW;IACb;AACF;AAGO,MAAM,aAAa;IACxB,UAAU;IACV,cAAc;IACd,eAAe;IACf,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,eAAe;AACjB;AA8BO,MAAM,kBAAkB;;;IAC7B,YACE,OAAe,EACf,AAAO,IAAa,EACpB,AAAO,IAAU,CACjB;QACA,KAAK,CAAC,eAHC,OAAA,WACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,SAAS,kBAAkB,MAAc,EAAE,WAAmB,EAAE;IACrE,MAAM,UAAU,OAAO,MAAM;IAC7B,MAAM,WAAW,SAAS;IAC1B,MAAM,YAAY,SAAS;IAE3B,IAAI,cAAc,EAAE,EAAE;QACpB,OAAO,SAAS,QAAQ;IAC1B;IAEA,MAAM,eAAe,UAAU,QAAQ,GAAG,QAAQ,CAAC,UAAU;IAC7D,MAAM,mBAAmB,aAAa,OAAO,CAAC,OAAO;IAErD,IAAI,qBAAqB,IAAI;QAC3B,OAAO,SAAS,QAAQ;IAC1B;IAEA,OAAO,GAAG,SAAS,CAAC,EAAE,kBAAkB;AAC1C;AAGO,SAAS,iBAAiB,MAAc,EAAE,WAAmB,EAAE;IACpE,MAAM,CAAC,OAAO,WAAW,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC;IAC5C,MAAM,iBAAiB,SAAS,MAAM,CAAC,UAAU,KAAK,KAAK,CAAC,GAAG;IAC/D,OAAO,OAAO,QAAQ;AACxB", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/providers/Web3Provider.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { WagmiProvider } from 'wagmi';\nimport { RainbowKitProvider, darkTheme, lightTheme } from '@rainbow-me/rainbowkit';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { config } from '@/lib/web3';\n\n// Import RainbowKit styles\nimport '@rainbow-me/rainbowkit/styles.css';\n\nconst queryClient = new QueryClient();\n\ninterface Web3ProviderProps {\n  children: ReactNode;\n}\n\nexport function Web3Provider({ children }: Web3ProviderProps) {\n  return (\n    <WagmiProvider config={config}>\n      <QueryClientProvider client={queryClient}>\n        <RainbowKitProvider\n          theme={{\n            lightMode: lightTheme({\n              accentColor: '#3b82f6',\n              accentColorForeground: 'white',\n              borderRadius: 'medium',\n              fontStack: 'system',\n            }),\n            darkMode: darkTheme({\n              accentColor: '#3b82f6',\n              accentColorForeground: 'white',\n              borderRadius: 'medium',\n              fontStack: 'system',\n            }),\n          }}\n          appInfo={{\n            appName: 'ManageLife',\n            learnMoreUrl: 'https://managelife.com/learn',\n          }}\n        >\n          {children}\n        </RainbowKitProvider>\n      </QueryClientProvider>\n    </WagmiProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AANA;;;;;;;AAWA,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW;AAM5B,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBACE,8OAAC,+IAAA,CAAA,gBAAa;QAAC,QAAQ,kHAAA,CAAA,SAAM;kBAC3B,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,8OAAC,8KAAA,CAAA,qBAAkB;gBACjB,OAAO;oBACL,WAAW,CAAA,GAAA,0KAAA,CAAA,aAAU,AAAD,EAAE;wBACpB,aAAa;wBACb,uBAAuB;wBACvB,cAAc;wBACd,WAAW;oBACb;oBACA,UAAU,CAAA,GAAA,0KAAA,CAAA,YAAS,AAAD,EAAE;wBAClB,aAAa;wBACb,uBAAuB;wBACvB,cAAc;wBACd,WAAW;oBACb;gBACF;gBACA,SAAS;oBACP,SAAS;oBACT,cAAc;gBAChB;0BAEC;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}