'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Building2,
  Home,
  Search,
  Users,
  Calendar,
  TrendingUp,
  Coins,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  AlertCircle,
  CheckCircle,
  Gift,
  Bell
} from 'lucide-react';
import { UserRole } from '@/types';
import { DASHBOARD_NAV_ITEMS, getRoleDisplayName, getRoleColor } from '@/constants';
import { useAuth } from '@/contexts/AuthContext';
import RewardCenter from '@/components/rewards/RewardCenter';
import Leaderboard from '@/components/rewards/Leaderboard';
import HomeownerDashboard from '@/components/dashboard/homeowner/HomeownerDashboard';
import RenterDashboard from '@/components/dashboard/renter/RenterDashboard';
import BuyerDashboard from '@/components/dashboard/buyer/BuyerDashboard';
import PortfolioManagerDashboard from '@/components/dashboard/portfolio-manager/PortfolioManagerDashboard';
import CommunityMemberDashboard from '@/components/dashboard/community-member/CommunityMemberDashboard';

export default function DashboardPage() {
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isWelcome = searchParams.get('welcome') === 'true';

  const [activeRole, setActiveRole] = useState<UserRole>('buyer');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showWelcome, setShowWelcome] = useState(isWelcome);
  const [activeTab, setActiveTab] = useState<'overview' | 'rewards' | 'leaderboard'>('overview');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Set initial active role when user data loads
  useEffect(() => {
    if (user && user.roles.length > 0) {
      setActiveRole(user.roles[0]);
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error if no user
  if (!user) {
    return null; // Will redirect to login
  }

  const navItems = DASHBOARD_NAV_ITEMS[activeRole] || [];

  return (
    <div className="min-h-screen bg-gray-50 lg:flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:z-auto lg:flex-shrink-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold gradient-text">ManageLife</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{user.name || 'User'}</h3>
              <p className="text-sm text-gray-600">{user.email || user.walletAddress}</p>
              {user.walletAddress && (
                <p className="text-xs text-gray-500 font-mono">
                  {user.walletAddress.slice(0, 6)}...{user.walletAddress.slice(-4)}
                </p>
              )}
            </div>
          </div>

          {/* Role Selector */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Active Role</label>
            <select
              value={activeRole}
              onChange={(e) => setActiveRole(e.target.value as UserRole)}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {user.roles.map((role) => (
                <option key={role} value={role}>
                  {getRoleDisplayName(role)}
                </option>
              ))}
            </select>
          </div>

          {/* $MLIFE Balance */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">$MLIFE Balance</span>
              <Coins className="w-4 h-4 text-blue-600" />
            </div>
            <p className="text-lg font-bold text-blue-600">{user.mlifeBalance.toLocaleString()}</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-6 py-4">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const IconComponent = {
                Home,
                Building: Building2,
                Building2,
                Coins,
                FileText,
                Search,
                Users,
                Calendar,
                TrendingUp,
                Bell,
                // Add more icon mappings as needed
              }[item.icon as keyof typeof import('lucide-react')] || Home;

              return (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom Actions */}
        <div className="p-6 border-t border-gray-200">
          <div className="space-y-2">
            <Link
              href="/settings"
              className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors w-full text-left"
            >
              <LogOut className="w-5 h-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:flex lg:flex-col lg:min-w-0">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-500 hover:text-gray-700"
              >
                <Menu className="w-6 h-6" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                {getRoleDisplayName(activeRole)} Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(activeRole)}`}>
                {getRoleDisplayName(activeRole)}
              </span>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          <div className="max-w-7xl mx-auto">
            {/* Tab Navigation */}
            <div className="flex justify-center mb-8">
              <div className="bg-gray-100 rounded-lg p-1 flex">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`px-6 py-2 rounded-md font-medium transition-colors ${
                    activeTab === 'overview'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Home className="w-4 h-4 inline mr-2" />
                  Overview
                </button>
                <button
                  onClick={() => setActiveTab('rewards')}
                  className={`px-6 py-2 rounded-md font-medium transition-colors ${
                    activeTab === 'rewards'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Gift className="w-4 h-4 inline mr-2" />
                  Rewards
                </button>
                <button
                  onClick={() => setActiveTab('leaderboard')}
                  className={`px-6 py-2 rounded-md font-medium transition-colors ${
                    activeTab === 'leaderboard'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <TrendingUp className="w-4 h-4 inline mr-2" />
                  Leaderboard
                </button>
              </div>
            </div>
            {/* Tab Content */}
            {activeTab === 'overview' && (
              <>
                {/* Welcome Message for New Users */}
                {showWelcome && (
                  <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
                    <div className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-green-900 mb-2">
                          Welcome to ManageLife! 🎉
                        </h3>
                        <p className="text-green-700 mb-4">
                          Your account has been created successfully. You've received 1,000 $MLIFE tokens as a welcome bonus!
                        </p>
                        <button
                          onClick={() => setShowWelcome(false)}
                          className="text-green-600 hover:text-green-700 font-medium text-sm"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white mb-8">
                  <h2 className="text-2xl font-bold mb-2">
                    Welcome back, {user.name || 'User'}!
                  </h2>
                  <p className="text-blue-100 mb-4">
                    You're currently viewing your {getRoleDisplayName(activeRole).toLowerCase()} dashboard.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                      <h3 className="font-semibold mb-1">$MLIFE Balance</h3>
                      <p className="text-2xl font-bold">{user.mlifeBalance.toLocaleString()}</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                      <h3 className="font-semibold mb-1">Active Roles</h3>
                      <p className="text-2xl font-bold">{user.roles.length}</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-4 backdrop-blur-sm">
                      <h3 className="font-semibold mb-1">Member Since</h3>
                      <p className="text-2xl font-bold">{new Date(user.joinedAt).getFullYear()}</p>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  {navItems.slice(1, 5).map((item) => {
                    const IconComponent = {
                      Home,
                      Building: Building2,
                      Building2,
                      Coins,
                      FileText,
                      Search,
                      Users,
                      Calendar,
                      TrendingUp,
                      Bell,
                    }[item.icon as keyof typeof import('lucide-react')] || Home;

                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow card-hover"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{item.label}</h3>
                            <p className="text-sm text-gray-600">Quick access</p>
                          </div>
                        </div>
                      </Link>
                    );
                  })}
                </div>

                {/* Role-specific dashboard */}
                {activeRole === 'homeowner' && <HomeownerDashboard />}
                {activeRole === 'renter' && <RenterDashboard />}
                {activeRole === 'buyer' && <BuyerDashboard />}
                {activeRole === 'portfolio-manager' && <PortfolioManagerDashboard />}
                {activeRole === 'community-member' && <CommunityMemberDashboard />}
              </>
            )}

            {/* Rewards Tab */}
            {activeTab === 'rewards' && <RewardCenter />}

            {/* Leaderboard Tab */}
            {activeTab === 'leaderboard' && <Leaderboard />}
          </div>
        </main>
      </div>
    </div>
  );
}
