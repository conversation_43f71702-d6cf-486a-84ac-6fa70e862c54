{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/marketplace/PropertyFilters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Search, Filter, X, MapPin, DollarSign, Home, Calendar } from 'lucide-react';\n\ninterface FilterState {\n  searchTerm: string;\n  location: string;\n  propertyType: string;\n  listingType: 'all' | 'sale' | 'rent';\n  priceRange: {\n    min: number;\n    max: number;\n  };\n  bedrooms: string;\n  bathrooms: string;\n  isTokenized: boolean | null;\n  amenities: string[];\n}\n\ninterface PropertyFiltersProps {\n  filters: FilterState;\n  onFiltersChange: (filters: FilterState) => void;\n  onClearFilters: () => void;\n}\n\nconst PROPERTY_TYPES = [\n  { value: '', label: 'All Types' },\n  { value: 'house', label: 'House' },\n  { value: 'apartment', label: 'Apartment' },\n  { value: 'condo', label: 'Condominium' },\n  { value: 'commercial', label: 'Commercial' },\n];\n\nconst BEDROOM_OPTIONS = [\n  { value: '', label: 'Any' },\n  { value: '0', label: 'Studio' },\n  { value: '1', label: '1 Bedroom' },\n  { value: '2', label: '2 Bedrooms' },\n  { value: '3', label: '3 Bedrooms' },\n  { value: '4', label: '4+ Bedrooms' },\n];\n\nconst BATHROOM_OPTIONS = [\n  { value: '', label: 'Any' },\n  { value: '1', label: '1 Bathroom' },\n  { value: '2', label: '2 Bathrooms' },\n  { value: '3', label: '3 Bathrooms' },\n  { value: '4', label: '4+ Bathrooms' },\n];\n\nconst AMENITIES = [\n  'Air Conditioning',\n  'Balcony',\n  'Dishwasher',\n  'Elevator',\n  'Fitness Center',\n  'Garage',\n  'Garden',\n  'Laundry in Unit',\n  'Parking',\n  'Pet Friendly',\n  'Pool',\n  'Rooftop Terrace',\n  'Security',\n  'Storage',\n];\n\nexport default function PropertyFilters({ filters, onFiltersChange, onClearFilters }: PropertyFiltersProps) {\n  const [showAdvanced, setShowAdvanced] = useState(false);\n\n  const updateFilter = (key: keyof FilterState, value: any) => {\n    onFiltersChange({\n      ...filters,\n      [key]: value,\n    });\n  };\n\n  const updatePriceRange = (type: 'min' | 'max', value: number) => {\n    onFiltersChange({\n      ...filters,\n      priceRange: {\n        ...filters.priceRange,\n        [type]: value,\n      },\n    });\n  };\n\n  const toggleAmenity = (amenity: string) => {\n    const newAmenities = filters.amenities.includes(amenity)\n      ? filters.amenities.filter(a => a !== amenity)\n      : [...filters.amenities, amenity];\n    \n    updateFilter('amenities', newAmenities);\n  };\n\n  const hasActiveFilters = () => {\n    return (\n      filters.searchTerm ||\n      filters.location ||\n      filters.propertyType ||\n      filters.listingType !== 'all' ||\n      filters.priceRange.min > 0 ||\n      filters.priceRange.max < 10000000 ||\n      filters.bedrooms ||\n      filters.bathrooms ||\n      filters.isTokenized !== null ||\n      filters.amenities.length > 0\n    );\n  };\n\n  return (\n    <div className=\"bg-white border-b border-gray-200\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Main Search Bar */}\n        <div className=\"mb-6\">\n          <div className=\"relative max-w-2xl mx-auto\">\n            <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search by location, property type, or features...\"\n              value={filters.searchTerm}\n              onChange={(e) => updateFilter('searchTerm', e.target.value)}\n              className=\"w-full pl-12 pr-4 py-4 rounded-xl border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg\"\n            />\n          </div>\n        </div>\n\n        {/* Quick Filters */}\n        <div className=\"flex flex-wrap items-center gap-4 mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"w-5 h-5 text-gray-600\" />\n            <span className=\"font-medium text-gray-900\">Filters:</span>\n          </div>\n          \n          {/* Listing Type */}\n          <select\n            value={filters.listingType}\n            onChange={(e) => updateFilter('listingType', e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"sale\">For Sale</option>\n            <option value=\"rent\">For Rent</option>\n          </select>\n\n          {/* Property Type */}\n          <select\n            value={filters.propertyType}\n            onChange={(e) => updateFilter('propertyType', e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            {PROPERTY_TYPES.map(type => (\n              <option key={type.value} value={type.value}>{type.label}</option>\n            ))}\n          </select>\n\n          {/* NFT Filter */}\n          <select\n            value={filters.isTokenized === null ? '' : filters.isTokenized.toString()}\n            onChange={(e) => updateFilter('isTokenized', e.target.value === '' ? null : e.target.value === 'true')}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Properties</option>\n            <option value=\"true\">NFT Properties Only</option>\n            <option value=\"false\">Traditional Properties</option>\n          </select>\n\n          {/* Advanced Filters Toggle */}\n          <button\n            onClick={() => setShowAdvanced(!showAdvanced)}\n            className=\"px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors font-medium\"\n          >\n            {showAdvanced ? 'Hide' : 'More'} Filters\n          </button>\n\n          {/* Clear Filters */}\n          {hasActiveFilters() && (\n            <button\n              onClick={onClearFilters}\n              className=\"px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors font-medium flex items-center\"\n            >\n              <X className=\"w-4 h-4 mr-1\" />\n              Clear All\n            </button>\n          )}\n        </div>\n\n        {/* Advanced Filters */}\n        {showAdvanced && (\n          <div className=\"bg-gray-50 rounded-xl p-6 space-y-6\">\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {/* Location */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <MapPin className=\"w-4 h-4 inline mr-1\" />\n                  Location\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"City, State, or ZIP\"\n                  value={filters.location}\n                  onChange={(e) => updateFilter('location', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              {/* Bedrooms */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  <Home className=\"w-4 h-4 inline mr-1\" />\n                  Bedrooms\n                </label>\n                <select\n                  value={filters.bedrooms}\n                  onChange={(e) => updateFilter('bedrooms', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {BEDROOM_OPTIONS.map(option => (\n                    <option key={option.value} value={option.value}>{option.label}</option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Bathrooms */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Bathrooms\n                </label>\n                <select\n                  value={filters.bathrooms}\n                  onChange={(e) => updateFilter('bathrooms', e.target.value)}\n                  className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  {BATHROOM_OPTIONS.map(option => (\n                    <option key={option.value} value={option.value}>{option.label}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Price Range */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <DollarSign className=\"w-4 h-4 inline mr-1\" />\n                Price Range\n              </label>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min Price\"\n                    value={filters.priceRange.min || ''}\n                    onChange={(e) => updatePriceRange('min', parseInt(e.target.value) || 0)}\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max Price\"\n                    value={filters.priceRange.max === 10000000 ? '' : filters.priceRange.max}\n                    onChange={(e) => updatePriceRange('max', parseInt(e.target.value) || 10000000)}\n                    className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Amenities */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Amenities\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n                {AMENITIES.map(amenity => (\n                  <label key={amenity} className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={filters.amenities.includes(amenity)}\n                      onChange={() => toggleAmenity(amenity)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2\"\n                    />\n                    <span className=\"text-sm text-gray-700\">{amenity}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Active Filters Display */}\n        {hasActiveFilters() && (\n          <div className=\"mt-4 flex flex-wrap gap-2\">\n            {filters.searchTerm && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                Search: \"{filters.searchTerm}\"\n                <button\n                  onClick={() => updateFilter('searchTerm', '')}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            )}\n            {filters.location && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                Location: {filters.location}\n                <button\n                  onClick={() => updateFilter('location', '')}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            )}\n            {filters.listingType !== 'all' && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                {filters.listingType === 'sale' ? 'For Sale' : 'For Rent'}\n                <button\n                  onClick={() => updateFilter('listingType', 'all')}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            )}\n            {filters.isTokenized !== null && (\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                {filters.isTokenized ? 'NFT Properties' : 'Traditional Properties'}\n                <button\n                  onClick={() => updateFilter('isTokenized', null)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            )}\n            {filters.amenities.map(amenity => (\n              <span key={amenity} className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800\">\n                {amenity}\n                <button\n                  onClick={() => toggleAmenity(amenity)}\n                  className=\"ml-2 text-green-600 hover:text-green-800\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA0BA,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAI,OAAO;IAAY;IAChC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAS,OAAO;IAAc;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAED,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAI,OAAO;IAAM;IAC1B;QAAE,OAAO;QAAK,OAAO;IAAS;IAC9B;QAAE,OAAO;QAAK,OAAO;IAAY;IACjC;QAAE,OAAO;QAAK,OAAO;IAAa;IAClC;QAAE,OAAO;QAAK,OAAO;IAAa;IAClC;QAAE,OAAO;QAAK,OAAO;IAAc;CACpC;AAED,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAI,OAAO;IAAM;IAC1B;QAAE,OAAO;QAAK,OAAO;IAAa;IAClC;QAAE,OAAO;QAAK,OAAO;IAAc;IACnC;QAAE,OAAO;QAAK,OAAO;IAAc;IACnC;QAAE,OAAO;QAAK,OAAO;IAAe;CACrC;AAED,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,gBAAgB,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAwB;IACxG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,CAAC,KAAwB;QAC5C,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,mBAAmB,CAAC,MAAqB;QAC7C,gBAAgB;YACd,GAAG,OAAO;YACV,YAAY;gBACV,GAAG,QAAQ,UAAU;gBACrB,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAAe,QAAQ,SAAS,CAAC,QAAQ,CAAC,WAC5C,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,WACpC;eAAI,QAAQ,SAAS;YAAE;SAAQ;QAEnC,aAAa,aAAa;IAC5B;IAEA,MAAM,mBAAmB;QACvB,OACE,QAAQ,UAAU,IAClB,QAAQ,QAAQ,IAChB,QAAQ,YAAY,IACpB,QAAQ,WAAW,KAAK,SACxB,QAAQ,UAAU,CAAC,GAAG,GAAG,KACzB,QAAQ,UAAU,CAAC,GAAG,GAAG,YACzB,QAAQ,QAAQ,IAChB,QAAQ,SAAS,IACjB,QAAQ,WAAW,KAAK,QACxB,QAAQ,SAAS,CAAC,MAAM,GAAG;IAE/B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU;gCACzB,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;sCAI9C,8OAAC;4BACC,OAAO,QAAQ,WAAW;4BAC1B,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC3D,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;;;;;;;sCAIvB,8OAAC;4BACC,OAAO,QAAQ,YAAY;4BAC3B,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC5D,WAAU;sCAET,eAAe,GAAG,CAAC,CAAA,qBAClB,8OAAC;oCAAwB,OAAO,KAAK,KAAK;8CAAG,KAAK,KAAK;mCAA1C,KAAK,KAAK;;;;;;;;;;sCAK3B,8OAAC;4BACC,OAAO,QAAQ,WAAW,KAAK,OAAO,KAAK,QAAQ,WAAW,CAAC,QAAQ;4BACvE,UAAU,CAAC,IAAM,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;4BAC/F,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,8OAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,8OAAC;oCAAO,OAAM;8CAAQ;;;;;;;;;;;;sCAIxB,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAU;;gCAET,eAAe,SAAS;gCAAO;;;;;;;wBAIjC,oCACC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;gBAOnC,8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG5C,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;4CACxD,WAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG1C,8OAAC;4CACC,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;4CACxD,WAAU;sDAET,gBAAgB,GAAG,CAAC,CAAA,uBACnB,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAAG,OAAO,KAAK;mDAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;8CAM/B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;4CACzD,WAAU;sDAET,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAAG,OAAO,KAAK;mDAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAOjC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAGhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG,IAAI;gDACjC,UAAU,CAAC,IAAM,iBAAiB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACrE,WAAU;;;;;;;;;;;sDAGd,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG,KAAK,WAAW,KAAK,QAAQ,UAAU,CAAC,GAAG;gDACxE,UAAU,CAAC,IAAM,iBAAiB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACrE,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAA,wBACb,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC;oDACpC,UAAU,IAAM,cAAc;oDAC9B,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;;2CAP/B;;;;;;;;;;;;;;;;;;;;;;gBAgBrB,oCACC,8OAAC;oBAAI,WAAU;;wBACZ,QAAQ,UAAU,kBACjB,8OAAC;4BAAK,WAAU;;gCAAoF;gCACxF,QAAQ,UAAU;gCAAC;8CAC7B,8OAAC;oCACC,SAAS,IAAM,aAAa,cAAc;oCAC1C,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIlB,QAAQ,QAAQ,kBACf,8OAAC;4BAAK,WAAU;;gCAAoF;gCACvF,QAAQ,QAAQ;8CAC3B,8OAAC;oCACC,SAAS,IAAM,aAAa,YAAY;oCACxC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIlB,QAAQ,WAAW,KAAK,uBACvB,8OAAC;4BAAK,WAAU;;gCACb,QAAQ,WAAW,KAAK,SAAS,aAAa;8CAC/C,8OAAC;oCACC,SAAS,IAAM,aAAa,eAAe;oCAC3C,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIlB,QAAQ,WAAW,KAAK,sBACvB,8OAAC;4BAAK,WAAU;;gCACb,QAAQ,WAAW,GAAG,mBAAmB;8CAC1C,8OAAC;oCACC,SAAS,IAAM,aAAa,eAAe;oCAC3C,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIlB,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAA,wBACrB,8OAAC;gCAAmB,WAAU;;oCAC3B;kDACD,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BANN;;;;;;;;;;;;;;;;;;;;;;AAezB", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n// Utility function for merging Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency values\nexport function formatCurrency(amount: number, currency: 'USD' | 'ETH' | 'MLIFE' = 'USD'): string {\n  switch (currency) {\n    case 'USD':\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n      }).format(amount);\n    case 'ETH':\n      return `${amount.toFixed(4)} ETH`;\n    case 'MLIFE':\n      return `${amount.toLocaleString()} $MLIFE`;\n    default:\n      return amount.toString();\n  }\n}\n\n// Format wallet address\nexport function formatWalletAddress(address: string, chars: number = 4): string {\n  if (!address) return '';\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`;\n}\n\n// Format date\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n}\n\n// Format time only\nexport function formatTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format date with time\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format time ago (relative time)\nexport function formatTimeAgo(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7);\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30);\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365);\n  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;\n}\n\n// Calculate days between dates\nexport function daysBetween(date1: Date, date2: Date): number {\n  const oneDay = 24 * 60 * 60 * 1000;\n  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));\n}\n\n// Check if date is overdue\nexport function isOverdue(dueDate: Date): boolean {\n  return new Date() > new Date(dueDate);\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Validate Ethereum address\nexport function isValidEthereumAddress(address: string): boolean {\n  return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// Convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result as string);\n    reader.onerror = error => reject(error);\n  });\n}\n\n// Get property status color\nexport function getPropertyStatusColor(status: string): string {\n  switch (status) {\n    case 'available':\n      return 'text-green-600 bg-green-100';\n    case 'rented':\n      return 'text-blue-600 bg-blue-100';\n    case 'sold':\n      return 'text-gray-600 bg-gray-100';\n    case 'maintenance':\n      return 'text-yellow-600 bg-yellow-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get maintenance priority color\nexport function getMaintenancePriorityColor(priority: string): string {\n  switch (priority) {\n    case 'urgent':\n      return 'text-red-600 bg-red-100';\n    case 'high':\n      return 'text-orange-600 bg-orange-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get payment status color\nexport function getPaymentStatusColor(status: string): string {\n  switch (status) {\n    case 'paid':\n      return 'text-green-600 bg-green-100';\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'overdue':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Calculate annual yield\nexport function calculateAnnualYield(monthlyRent: number, propertyValue: number): number {\n  if (propertyValue === 0) return 0;\n  return ((monthlyRent * 12) / propertyValue) * 100;\n}\n\n// Format property type\nexport function formatPropertyType(type: string): string {\n  return type.charAt(0).toUpperCase() + type.slice(1);\n}\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAoC,KAAK;IACtF,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;gBACpC,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QACZ,KAAK;YACH,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC;QACnC,KAAK;YACH,OAAO,GAAG,OAAO,cAAc,GAAG,OAAO,CAAC;QAC5C;YACE,OAAO,OAAO,QAAQ;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAgB,CAAC;IACpE,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACpE;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,cAAc,IAAmB;IAC/C,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,eAAe,IAAI,MAAM,GAAG,IAAI,CAAC;IAClE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;AAC/D;AAGO,SAAS,YAAY,KAAW,EAAE,KAAW;IAClD,MAAM,SAAS,KAAK,KAAK,KAAK;IAC9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI;AACnE;AAGO,SAAS,UAAU,OAAa;IACrC,OAAO,IAAI,SAAS,IAAI,KAAK;AAC/B;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,uBAAuB,OAAe;IACpD,OAAO,sBAAsB,IAAI,CAAC;AACpC;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;QAC3C,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,uBAAuB,MAAc;IACnD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,4BAA4B,QAAgB;IAC1D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,sBAAsB,MAAc;IAClD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,qBAAqB,WAAmB,EAAE,aAAqB;IAC7E,IAAI,kBAAkB,GAAG,OAAO;IAChC,OAAO,AAAE,cAAc,KAAM,gBAAiB;AAChD;AAGO,SAAS,mBAAmB,IAAY;IAC7C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/marketplace/PropertyCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { \n  MapPin, \n  Bed, \n  Bath, \n  Square, \n  Heart, \n  Coins, \n  TrendingUp,\n  Calendar,\n  Star,\n  Eye\n} from 'lucide-react';\nimport { Property } from '@/types';\nimport { formatCurrency, formatDate, calculateAnnualYield } from '@/utils';\n\ninterface PropertyCardProps {\n  property: Property & {\n    monthlyRent?: number;\n    owner?: {\n      name: string;\n      rating: number;\n    };\n    views?: number;\n    listedDate?: Date;\n  };\n  onFavoriteToggle?: (propertyId: string) => void;\n  isFavorite?: boolean;\n  showOwnerInfo?: boolean;\n  showStats?: boolean;\n}\n\nexport default function PropertyCard({ \n  property, \n  onFavoriteToggle, \n  isFavorite = false,\n  showOwnerInfo = false,\n  showStats = false\n}: PropertyCardProps) {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n\n  const handleFavoriteClick = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (onFavoriteToggle) {\n      onFavoriteToggle(property.id);\n    }\n  };\n\n  const annualYield = property.monthlyRent && property.price \n    ? calculateAnnualYield(property.monthlyRent, property.price)\n    : null;\n\n  return (\n    <Link href={`/marketplace/${property.id}`}>\n      <div className=\"bg-white rounded-xl shadow-lg overflow-hidden card-hover border border-gray-100 group\">\n        {/* Property Image */}\n        <div className=\"relative h-48 bg-gray-200 overflow-hidden\">\n          {!imageError ? (\n            <div className=\"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n              <div className=\"text-gray-400 text-center\">\n                <div className=\"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center\">\n                  <Square className=\"w-8 h-8\" />\n                </div>\n                <p className=\"text-sm\">Property Image</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n              <span className=\"text-gray-400\">Image not available</span>\n            </div>\n          )}\n          \n          {/* Overlay gradient */}\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n          \n          {/* Badges */}\n          <div className=\"absolute top-4 left-4 flex flex-col space-y-2\">\n            {property.featured && (\n              <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                Featured\n              </div>\n            )}\n            <div className={`px-3 py-1 rounded-full text-sm font-semibold ${\n              property.status === 'available' \n                ? 'bg-green-600 text-white' \n                : 'bg-yellow-600 text-white'\n            }`}>\n              {property.status === 'available' ? 'Available' : 'Pending'}\n            </div>\n          </div>\n\n          {/* NFT Badge */}\n          {property.isTokenized && (\n            <div className=\"absolute top-4 right-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center\">\n              <Coins className=\"w-3 h-3 mr-1\" />\n              NFT\n            </div>\n          )}\n\n          {/* Favorite Button */}\n          <button\n            onClick={handleFavoriteClick}\n            className=\"absolute bottom-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors group-hover:scale-110 transform duration-200\"\n          >\n            <Heart className={`w-5 h-5 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'}`} />\n          </button>\n\n          {/* Stats Overlay */}\n          {showStats && property.views && (\n            <div className=\"absolute bottom-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-sm flex items-center\">\n              <Eye className=\"w-3 h-3 mr-1\" />\n              {property.views}\n            </div>\n          )}\n        </div>\n\n        {/* Property Details */}\n        <div className=\"p-6\">\n          {/* Header */}\n          <div className=\"flex items-start justify-between mb-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-blue-600 transition-colors\">\n              {property.title}\n            </h3>\n            <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ml-2 ${\n              property.listingType === 'sale' \n                ? 'bg-blue-100 text-blue-600' \n                : 'bg-green-100 text-green-600'\n            }`}>\n              {property.listingType === 'sale' ? 'For Sale' : 'For Rent'}\n            </span>\n          </div>\n          \n          {/* Location */}\n          <div className=\"flex items-center text-gray-600 mb-3\">\n            <MapPin className=\"w-4 h-4 mr-1 flex-shrink-0\" />\n            <span className=\"text-sm line-clamp-1\">{property.city}, {property.state}</span>\n          </div>\n\n          {/* Description */}\n          <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n            {property.description}\n          </p>\n\n          {/* Property Features */}\n          <div className=\"flex items-center space-x-4 mb-4 text-sm text-gray-600\">\n            {property.bedrooms > 0 && (\n              <div className=\"flex items-center\">\n                <Bed className=\"w-4 h-4 mr-1\" />\n                <span>{property.bedrooms}</span>\n              </div>\n            )}\n            <div className=\"flex items-center\">\n              <Bath className=\"w-4 h-4 mr-1\" />\n              <span>{property.bathrooms}</span>\n            </div>\n            <div className=\"flex items-center\">\n              <Square className=\"w-4 h-4 mr-1\" />\n              <span>{property.squareFeet?.toLocaleString()} sqft</span>\n            </div>\n          </div>\n\n          {/* Owner Info */}\n          {showOwnerInfo && property.owner && (\n            <div className=\"flex items-center space-x-2 mb-4 p-3 bg-gray-50 rounded-lg\">\n              <div className=\"w-8 h-8 bg-gray-300 rounded-full\"></div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900\">{property.owner.name}</p>\n                <div className=\"flex items-center text-xs text-gray-600\">\n                  <Star className=\"w-3 h-3 text-yellow-400 mr-1\" />\n                  <span>{property.owner.rating}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Price and Metrics */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {formatCurrency(property.price, property.currency)}\n                {property.listingType === 'rent' && <span className=\"text-sm font-normal text-gray-600\">/month</span>}\n              </p>\n              \n              {/* Additional Info */}\n              <div className=\"flex items-center space-x-4 mt-1\">\n                {property.isTokenized && (\n                  <span className=\"text-xs text-green-600 font-medium\">\n                    Token #{property.nftTokenId}\n                  </span>\n                )}\n                {property.listedDate && (\n                  <span className=\"text-xs text-gray-500\">\n                    Listed {formatDate(property.listedDate)}\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* Yield Badge */}\n            {annualYield && property.listingType === 'sale' && (\n              <div className=\"text-right\">\n                <div className=\"flex items-center text-green-600 text-sm font-semibold\">\n                  <TrendingUp className=\"w-4 h-4 mr-1\" />\n                  {annualYield.toFixed(1)}%\n                </div>\n                <p className=\"text-xs text-gray-500\">Annual Yield</p>\n              </div>\n            )}\n          </div>\n\n          {/* Monthly Rent for Sale Properties */}\n          {property.listingType === 'sale' && property.monthlyRent && (\n            <div className=\"mt-3 pt-3 border-t border-gray-200\">\n              <p className=\"text-sm text-gray-600\">\n                Rental Income: <span className=\"font-semibold text-gray-900\">\n                  {formatCurrency(property.monthlyRent, property.currency)}/month\n                </span>\n              </p>\n            </div>\n          )}\n\n          {/* Amenities Preview */}\n          {property.amenities && property.amenities.length > 0 && (\n            <div className=\"mt-3 pt-3 border-t border-gray-200\">\n              <div className=\"flex flex-wrap gap-1\">\n                {property.amenities.slice(0, 3).map((amenity, index) => (\n                  <span key={index} className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\">\n                    {amenity}\n                  </span>\n                ))}\n                {property.amenities.length > 3 && (\n                  <span className=\"text-xs text-gray-500 px-2 py-1\">\n                    +{property.amenities.length - 3} more\n                  </span>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAjBA;;;;;;AAmCe,SAAS,aAAa,EACnC,QAAQ,EACR,gBAAgB,EAChB,aAAa,KAAK,EAClB,gBAAgB,KAAK,EACrB,YAAY,KAAK,EACC;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,EAAE;QAC9B;IACF;IAEA,MAAM,cAAc,SAAS,WAAW,IAAI,SAAS,KAAK,GACtD,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,WAAW,EAAE,SAAS,KAAK,IACzD;IAEJ,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBACZ,CAAC,2BACA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;iDAI3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAKpC,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,QAAQ,kBAChB,8OAAC;oCAAI,WAAU;8CAAuG;;;;;;8CAIxH,8OAAC;oCAAI,WAAW,CAAC,6CAA6C,EAC5D,SAAS,MAAM,KAAK,cAChB,4BACA,4BACJ;8CACC,SAAS,MAAM,KAAK,cAAc,cAAc;;;;;;;;;;;;wBAKpD,SAAS,WAAW,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAMtC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,8BAA8B,iBAAiB;;;;;;;;;;;wBAI1F,aAAa,SAAS,KAAK,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCACd,SAAS,KAAK;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAK,WAAW,CAAC,8DAA8D,EAC9E,SAAS,WAAW,KAAK,SACrB,8BACA,+BACJ;8CACC,SAAS,WAAW,KAAK,SAAS,aAAa;;;;;;;;;;;;sCAKpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;;wCAAwB,SAAS,IAAI;wCAAC;wCAAG,SAAS,KAAK;;;;;;;;;;;;;sCAIzE,8OAAC;4BAAE,WAAU;sCACV,SAAS,WAAW;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,QAAQ,GAAG,mBACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAM,SAAS,QAAQ;;;;;;;;;;;;8CAG5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAM,SAAS,SAAS;;;;;;;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;;gDAAM,SAAS,UAAU,EAAE;gDAAiB;;;;;;;;;;;;;;;;;;;wBAKhD,iBAAiB,SAAS,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAqC,SAAS,KAAK,CAAC,IAAI;;;;;;sDACrE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAM,SAAS,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;;gDACV,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ;gDAChD,SAAS,WAAW,KAAK,wBAAU,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;sDAI1F,8OAAC;4CAAI,WAAU;;gDACZ,SAAS,WAAW,kBACnB,8OAAC;oDAAK,WAAU;;wDAAqC;wDAC3C,SAAS,UAAU;;;;;;;gDAG9B,SAAS,UAAU,kBAClB,8OAAC;oDAAK,WAAU;;wDAAwB;wDAC9B,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;;;;;;;gCAO7C,eAAe,SAAS,WAAW,KAAK,wBACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,YAAY,OAAO,CAAC;gDAAG;;;;;;;sDAE1B,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;wBAM1C,SAAS,WAAW,KAAK,UAAU,SAAS,WAAW,kBACtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;kDACpB,8OAAC;wCAAK,WAAU;;4CAC5B,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW,EAAE,SAAS,QAAQ;4CAAE;;;;;;;;;;;;;;;;;;wBAOhE,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,mBACjD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC5C,8OAAC;4CAAiB,WAAU;sDACzB;2CADQ;;;;;oCAIZ,SAAS,SAAS,CAAC,MAAM,GAAG,mBAC3B,8OAAC;wCAAK,WAAU;;4CAAkC;4CAC9C,SAAS,SAAS,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD", "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/hooks/useWeb3.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { useAccount, useChainId, useWriteContract, useReadContract, useWaitForTransactionReceipt } from 'wagmi';\nimport { parseEther, formatEther } from 'viem';\nimport { CONTRACT_ABIS, getContractAddress, TransactionType, Web3Error } from '@/lib/web3';\n\nexport function useWeb3() {\n  const { address, isConnected } = useAccount();\n  const chainId = useChainId();\n  const { writeContract } = useWriteContract();\n  \n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Helper function to handle contract writes\n  const executeTransaction = useCallback(async (\n    contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',\n    functionName: string,\n    args: any[] = [],\n    value?: bigint\n  ) => {\n    if (!isConnected || !address) {\n      throw new Web3Error('Wallet not connected');\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const contractAddress = getContractAddress(contractName, chainId);\n      let abi;\n\n      switch (contractName) {\n        case 'NFTi':\n        case 'NFTr':\n          abi = CONTRACT_ABIS.NFT;\n          break;\n        case 'MLIFE_TOKEN':\n          abi = CONTRACT_ABIS.ERC20;\n          break;\n        case 'MARKETPLACE':\n          abi = CONTRACT_ABIS.MARKETPLACE;\n          break;\n        case 'PROPERTY_REGISTRY':\n          abi = CONTRACT_ABIS.PROPERTY_REGISTRY;\n          break;\n        default:\n          throw new Web3Error('Unknown contract');\n      }\n\n      const result = await writeContract({\n        address: contractAddress as `0x${string}`,\n        abi,\n        functionName,\n        args,\n        value,\n      });\n\n      return result;\n    } catch (err: any) {\n      const errorMessage = err.message || 'Transaction failed';\n      setError(errorMessage);\n      throw new Web3Error(errorMessage, err.code, err.data);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [isConnected, address, chainId, writeContract]);\n\n  return {\n    address,\n    isConnected,\n    chainId,\n    isLoading,\n    error,\n    executeTransaction,\n    clearError: () => setError(null),\n  };\n}\n\n// Hook for reading contract data\nexport function useContractRead(\n  contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',\n  functionName: string,\n  args: any[] = []\n) {\n  const chainId = useChainId();\n  \n  const contractAddress = getContractAddress(contractName, chainId);\n  let abi;\n\n  switch (contractName) {\n    case 'NFTi':\n    case 'NFTr':\n      abi = CONTRACT_ABIS.NFT;\n      break;\n    case 'MLIFE_TOKEN':\n      abi = CONTRACT_ABIS.ERC20;\n      break;\n    case 'MARKETPLACE':\n      abi = CONTRACT_ABIS.MARKETPLACE;\n      break;\n    case 'PROPERTY_REGISTRY':\n      abi = CONTRACT_ABIS.PROPERTY_REGISTRY;\n      break;\n    default:\n      throw new Error('Unknown contract');\n  }\n\n  return useReadContract({\n    address: contractAddress as `0x${string}`,\n    abi,\n    functionName,\n    args,\n  });\n}\n\n// Hook for $MLIFE token operations\nexport function useMLifeToken() {\n  const { executeTransaction } = useWeb3();\n  const { address } = useAccount();\n  const chainId = useChainId();\n\n  // Get token balance\n  const { data: balance, refetch: refetchBalance } = useContractRead(\n    'MLIFE_TOKEN',\n    'balanceOf',\n    address ? [address] : []\n  );\n\n  // Transfer tokens\n  const transfer = useCallback(async (to: string, amount: string) => {\n    const amountWei = parseEther(amount);\n    return executeTransaction('MLIFE_TOKEN', 'transfer', [to, amountWei]);\n  }, [executeTransaction]);\n\n  // Approve tokens\n  const approve = useCallback(async (spender: string, amount: string) => {\n    const amountWei = parseEther(amount);\n    return executeTransaction('MLIFE_TOKEN', 'approve', [spender, amountWei]);\n  }, [executeTransaction]);\n\n  return {\n    balance: balance ? formatEther(balance as bigint) : '0',\n    transfer,\n    approve,\n    refetchBalance,\n  };\n}\n\n// Hook for NFT operations\nexport function useNFTOperations() {\n  const { executeTransaction } = useWeb3();\n  const { address } = useAccount();\n\n  // Mint NFTi (Property NFT)\n  const mintPropertyNFT = useCallback(async (tokenId: number, tokenURI: string) => {\n    if (!address) throw new Web3Error('Wallet not connected');\n    return executeTransaction('NFTi', 'mint', [address, tokenId, tokenURI]);\n  }, [executeTransaction, address]);\n\n  // Mint NFTr (Rental NFT)\n  const mintRentalNFT = useCallback(async (tokenId: number, tokenURI: string) => {\n    if (!address) throw new Web3Error('Wallet not connected');\n    return executeTransaction('NFTr', 'mint', [address, tokenId, tokenURI]);\n  }, [executeTransaction, address]);\n\n  // Transfer NFT\n  const transferNFT = useCallback(async (\n    contractType: 'NFTi' | 'NFTr',\n    from: string,\n    to: string,\n    tokenId: number\n  ) => {\n    return executeTransaction(contractType, 'transferFrom', [from, to, tokenId]);\n  }, [executeTransaction]);\n\n  // Get NFT owner\n  const getNFTOwner = useCallback((contractType: 'NFTi' | 'NFTr', tokenId: number) => {\n    return useContractRead(contractType, 'ownerOf', [tokenId]);\n  }, []);\n\n  return {\n    mintPropertyNFT,\n    mintRentalNFT,\n    transferNFT,\n    getNFTOwner,\n  };\n}\n\n// Hook for marketplace operations\nexport function useMarketplace() {\n  const { executeTransaction } = useWeb3();\n  const chainId = useChainId();\n\n  const { data: rawListings, isLoading } = useContractRead('MARKETPLACE', 'getActiveListings', []);\n\n  // List property for sale/rent\n  const listProperty = useCallback(async (\n    tokenId: number,\n    price: string,\n    isForRent: boolean\n  ) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'listProperty', [tokenId, priceWei, isForRent]);\n  }, [executeTransaction]);\n\n  // Buy property\n  const buyProperty = useCallback(async (listingId: number, price: string) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'buyProperty', [listingId], priceWei);\n  }, [executeTransaction]);\n\n  // Rent property\n  const rentProperty = useCallback(async (\n    listingId: number,\n    duration: number,\n    price: string\n  ) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'rentProperty', [listingId, duration], priceWei);\n  }, [executeTransaction]);\n\n  // Cancel listing\n  const cancelListing = useCallback(async (listingId: number) => {\n    return executeTransaction('MARKETPLACE', 'cancelListing', [listingId]);\n  }, [executeTransaction]);\n\n  // Get listing details\n  const getListing = useCallback((listingId: number) => {\n    return useContractRead('MARKETPLACE', 'getListing', [listingId]);\n  }, []);\n\n  // Make offer\n  const makeOffer = useCallback(async (listingId: number, offerPrice: string) => {\n    const offerWei = parseEther(offerPrice);\n    return executeTransaction('MARKETPLACE', 'makeOffer', [listingId], offerWei);\n  }, [executeTransaction]);\n\n  return {\n    properties: rawListings || [],\nisLoading,\n    listProperty,\n    buyProperty,\n    rentProperty,\n    cancelListing,\n    getListing,\n    makeOffer,\n  };\n}\n\n// Hook for property registry operations\nexport function usePropertyRegistry() {\n  const { executeTransaction } = useWeb3();\n\n  // Register new property\n  const registerProperty = useCallback(async (\n    propertyData: string,\n    location: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'registerProperty', [propertyData, location]);\n  }, [executeTransaction]);\n\n  // Tokenize property\n  const tokenizeProperty = useCallback(async (\n    propertyId: number,\n    tokenURI: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'tokenizeProperty', [propertyId, tokenURI]);\n  }, [executeTransaction]);\n\n  // Get property details\n  const getProperty = useCallback((propertyId: number) => {\n    return useContractRead('PROPERTY_REGISTRY', 'getProperty', [propertyId]);\n  }, []);\n\n  // Update property\n  const updateProperty = useCallback(async (\n    propertyId: number,\n    propertyData: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'updateProperty', [propertyId, propertyData]);\n  }, [executeTransaction]);\n\n  return {\n    registerProperty,\n    tokenizeProperty,\n    getProperty,\n    updateProperty,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;AAEO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD;IAEzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4CAA4C;IAC5C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACrC,cACA,cACA,OAAc,EAAE,EAChB;QAEA,IAAI,CAAC,eAAe,CAAC,SAAS;YAC5B,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QACtB;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;YACzD,IAAI;YAEJ,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,GAAG;oBACvB;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,KAAK;oBACzB;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,WAAW;oBAC/B;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,iBAAiB;oBACrC;gBACF;oBACE,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;YACxB;YAEA,MAAM,SAAS,MAAM,cAAc;gBACjC,SAAS;gBACT;gBACA;gBACA;gBACA;YACF;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,MAAM,eAAe,IAAI,OAAO,IAAI;YACpC,SAAS;YACT,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC,cAAc,IAAI,IAAI,EAAE,IAAI,IAAI;QACtD,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAa;QAAS;QAAS;KAAc;IAEjD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF;AAGO,SAAS,gBACd,YAAmF,EACnF,YAAoB,EACpB,OAAc,EAAE;IAEhB,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;IACzD,IAAI;IAEJ,OAAQ;QACN,KAAK;QACL,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,GAAG;YACvB;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,KAAK;YACzB;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,WAAW;YAC/B;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,iBAAiB;YACrC;QACF;YACE,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QACrB,SAAS;QACT;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,oBAAoB;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,cAAc,EAAE,GAAG,gBACjD,eACA,aACA,UAAU;QAAC;KAAQ,GAAG,EAAE;IAG1B,kBAAkB;IAClB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QAC9C,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC7B,OAAO,mBAAmB,eAAe,YAAY;YAAC;YAAI;SAAU;IACtE,GAAG;QAAC;KAAmB;IAEvB,iBAAiB;IACjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QAClD,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC7B,OAAO,mBAAmB,eAAe,WAAW;YAAC;YAAS;SAAU;IAC1E,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,SAAS,UAAU,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,WAAqB;QACpD;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAE7B,2BAA2B;IAC3B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QAC1D,IAAI,CAAC,SAAS,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QAClC,OAAO,mBAAmB,QAAQ,QAAQ;YAAC;YAAS;YAAS;SAAS;IACxE,GAAG;QAAC;QAAoB;KAAQ;IAEhC,yBAAyB;IACzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QACxD,IAAI,CAAC,SAAS,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QAClC,OAAO,mBAAmB,QAAQ,QAAQ;YAAC;YAAS;YAAS;SAAS;IACxE,GAAG;QAAC;QAAoB;KAAQ;IAEhC,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC9B,cACA,MACA,IACA;QAEA,OAAO,mBAAmB,cAAc,gBAAgB;YAAC;YAAM;YAAI;SAAQ;IAC7E,GAAG;QAAC;KAAmB;IAEvB,gBAAgB;IAChB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAA+B;QAC9D,OAAO,gBAAgB,cAAc,WAAW;YAAC;SAAQ;IAC3D,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,GAAG,gBAAgB,eAAe,qBAAqB,EAAE;IAE/F,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC/B,SACA,OACA;QAEA,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,gBAAgB;YAAC;YAAS;YAAU;SAAU;IACzF,GAAG;QAAC;KAAmB;IAEvB,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QACxD,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,eAAe;YAAC;SAAU,EAAE;IACvE,GAAG;QAAC;KAAmB;IAEvB,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC/B,WACA,UACA;QAEA,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,gBAAgB;YAAC;YAAW;SAAS,EAAE;IAClF,GAAG;QAAC;KAAmB;IAEvB,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,OAAO,mBAAmB,eAAe,iBAAiB;YAAC;SAAU;IACvE,GAAG;QAAC;KAAmB;IAEvB,sBAAsB;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,OAAO,gBAAgB,eAAe,cAAc;YAAC;SAAU;IACjE,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QACtD,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,aAAa;YAAC;SAAU,EAAE;IACrE,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,YAAY,eAAe,EAAE;QACjC;QACI;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAE/B,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,cACA;QAEA,OAAO,mBAAmB,qBAAqB,oBAAoB;YAAC;YAAc;SAAS;IAC7F,GAAG;QAAC;KAAmB;IAEvB,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,YACA;QAEA,OAAO,mBAAmB,qBAAqB,oBAAoB;YAAC;YAAY;SAAS;IAC3F,GAAG;QAAC;KAAmB;IAEvB,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,OAAO,gBAAgB,qBAAqB,eAAe;YAAC;SAAW;IACzE,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,YACA;QAEA,OAAO,mBAAmB,qBAAqB,kBAAkB;YAAC;YAAY;SAAa;IAC7F,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/marketplace/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport {\n  Building2,\n  ArrowLeft,\n  Grid3X3,\n  List,\n  SortAsc\n} from 'lucide-react';\nimport Navigation from '@/components/layout/Navigation';\nimport PropertyFilters from '@/components/marketplace/PropertyFilters';\nimport PropertyCard from '@/components/marketplace/PropertyCard';\n\nimport { useMarketplace } from '@/hooks/useWeb3';\n\n\nexport default function MarketplacePage() {\n  const { properties, isLoading } = useMarketplace();\n  const [filters, setFilters] = useState({\n    searchTerm: '',\n    location: '',\n    propertyType: '',\n    listingType: 'all' as 'all' | 'sale' | 'rent',\n    priceRange: { min: 0, max: 10000000 },\n    bedrooms: '',\n    bathrooms: '',\n    isTokenized: null as boolean | null,\n    amenities: [] as string[]\n  });\n\n  const [favorites, setFavorites] = useState<string[]>([]);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [sortBy, setSortBy] = useState<'price-low' | 'price-high' | 'newest' | 'oldest'>('newest');\n\n  const toggleFavorite = (propertyId: string) => {\n    setFavorites(prev =>\n      prev.includes(propertyId)\n        ? prev.filter(id => id !== propertyId)\n        : [...prev, propertyId]\n    );\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      searchTerm: '',\n      location: '',\n      propertyType: '',\n      listingType: 'all',\n      priceRange: { min: 0, max: 10000000 },\n      bedrooms: '',\n      bathrooms: '',\n      isTokenized: null,\n      amenities: []\n    });\n  };\n\n  if (isLoading) return <div>Loading properties...</div>;\n\n  const filteredProperties = (properties || []).filter(property => {\n    // Search term filter\n    const matchesSearch = !filters.searchTerm ||\n      property.title.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||\n      property.city.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||\n      property.description.toLowerCase().includes(filters.searchTerm.toLowerCase());\n\n    // Location filter\n    const matchesLocation = !filters.location ||\n      property.city.toLowerCase().includes(filters.location.toLowerCase()) ||\n      property.state.toLowerCase().includes(filters.location.toLowerCase()) ||\n      property.zipCode.includes(filters.location);\n\n    // Property type filter\n    const matchesPropertyType = !filters.propertyType || property.propertyType === filters.propertyType;\n\n    // Listing type filter\n    const matchesListingType = filters.listingType === 'all' || property.listingType === filters.listingType;\n\n    // Price range filter\n    const matchesPrice = property.price >= filters.priceRange.min && property.price <= filters.priceRange.max;\n\n    // Bedrooms filter\n    const matchesBedrooms = !filters.bedrooms ||\n      (filters.bedrooms === '4' ? property.bedrooms >= 4 : property.bedrooms.toString() === filters.bedrooms);\n\n    // Bathrooms filter\n    const matchesBathrooms = !filters.bathrooms ||\n      (filters.bathrooms === '4' ? property.bathrooms >= 4 : property.bathrooms.toString() === filters.bathrooms);\n\n    // NFT filter\n    const matchesTokenized = filters.isTokenized === null || property.isTokenized === filters.isTokenized;\n\n    // Amenities filter\n    const matchesAmenities = filters.amenities.length === 0 ||\n      filters.amenities.every(amenity => property.amenities.includes(amenity));\n\n    return matchesSearch && matchesLocation && matchesPropertyType && matchesListingType &&\n           matchesPrice && matchesBedrooms && matchesBathrooms && matchesTokenized && matchesAmenities;\n  });\n\n  // Sort properties\n  if (isLoading) return <div>Loading properties...</div>;\n\n  const sortedProperties = [...(filteredProperties || [])].sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'oldest':\n        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n      case 'newest':\n      default:\n        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n    }\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Home\n              </Link>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/auth/login\"\n                className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                Sign In\n              </Link>\n              <Link\n                href=\"/dashboard\"\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300\"\n              >\n                Dashboard\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n            Property Marketplace\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Discover tokenized real estate properties and investment opportunities\n          </p>\n          <div className=\"flex items-center justify-center space-x-8 text-blue-100\">\n            <div>\n              <p className=\"text-2xl font-bold\">{(properties || []).length}</p>\n              <p className=\"text-sm\">Properties</p>\n            </div>\n            <div>\n              <p className=\"text-2xl font-bold\">{(properties || []).filter(p => p.isTokenized).length}</p>\n              <p className=\"text-sm\">NFT Properties</p>\n            </div>\n            <div>\n              <p className=\"text-2xl font-bold\">{(properties || []).filter(p => p.listingType === 'sale').length}</p>\n              <p className=\"text-sm\">For Sale</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Filters */}\n      <PropertyFilters\n        filters={filters}\n        onFiltersChange={setFilters}\n        onClearFilters={clearFilters}\n      />\n\n      {/* Results Header */}\n      <section className=\"bg-white border-b border-gray-200 py-4\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <p className=\"text-gray-600\">\n                <span className=\"font-semibold text-gray-900\">{sortedProperties.length}</span> properties found\n              </p>\n              {filters.searchTerm && (\n                <p className=\"text-sm text-gray-500\">\n                  for \"{filters.searchTerm}\"\n                </p>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {/* Sort */}\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"price-low\">Price: Low to High</option>\n                <option value=\"price-high\">Price: High to Low</option>\n              </select>\n\n              {/* View Mode */}\n              <div className=\"flex border border-gray-300 rounded-lg overflow-hidden\">\n                <button\n                  onClick={() => setViewMode('grid')}\n                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}\n                >\n                  <Grid3X3 className=\"w-4 h-4\" />\n                </button>\n                <button\n                  onClick={() => setViewMode('list')}\n                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}\n                >\n                  <List className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Properties Grid/List */}\n      <section className=\"py-12\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          {sortedProperties.length > 0 ? (\n            <div className={viewMode === 'grid'\n              ? 'grid md:grid-cols-2 lg:grid-cols-3 gap-8'\n              : 'space-y-6'\n            }>\n              {sortedProperties.map((property) => (\n                <PropertyCard\n                  key={property.id}\n                  property={property}\n                  onFavoriteToggle={toggleFavorite}\n                  isFavorite={favorites.includes(property.id)}\n                  showOwnerInfo={viewMode === 'list'}\n                  showStats={true}\n                />\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <Building2 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No properties found</h3>\n              <p className=\"text-gray-600 mb-4\">Try adjusting your search criteria or filters</p>\n              <button\n                onClick={clearFilters}\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Clear All Filters\n              </button>\n            </div>\n          )}\n\n          {/* Load More Button */}\n          {sortedProperties.length > 0 && sortedProperties.length >= 12 && (\n            <div className=\"text-center mt-12\">\n              <button className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300\">\n                Load More Properties\n              </button>\n            </div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAQA;AACA;AAEA;AAfA;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,YAAY;QACZ,UAAU;QACV,cAAc;QACd,aAAa;QACb,YAAY;YAAE,KAAK;YAAG,KAAK;QAAS;QACpC,UAAU;QACV,WAAW;QACX,aAAa;QACb,WAAW,EAAE;IACf;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoD;IAEvF,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,YAAY;YACZ,UAAU;YACV,cAAc;YACd,aAAa;YACb,YAAY;gBAAE,KAAK;gBAAG,KAAK;YAAS;YACpC,UAAU;YACV,WAAW;YACX,aAAa;YACb,WAAW,EAAE;QACf;IACF;IAEA,IAAI,WAAW,qBAAO,8OAAC;kBAAI;;;;;;IAE3B,MAAM,qBAAqB,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAA;QACnD,qBAAqB;QACrB,MAAM,gBAAgB,CAAC,QAAQ,UAAU,IACvC,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OACpE,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OACnE,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW;QAE5E,kBAAkB;QAClB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IACvC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW,OACjE,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW,OAClE,SAAS,OAAO,CAAC,QAAQ,CAAC,QAAQ,QAAQ;QAE5C,uBAAuB;QACvB,MAAM,sBAAsB,CAAC,QAAQ,YAAY,IAAI,SAAS,YAAY,KAAK,QAAQ,YAAY;QAEnG,sBAAsB;QACtB,MAAM,qBAAqB,QAAQ,WAAW,KAAK,SAAS,SAAS,WAAW,KAAK,QAAQ,WAAW;QAExG,qBAAqB;QACrB,MAAM,eAAe,SAAS,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,IAAI,SAAS,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG;QAEzG,kBAAkB;QAClB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IACvC,CAAC,QAAQ,QAAQ,KAAK,MAAM,SAAS,QAAQ,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,OAAO,QAAQ,QAAQ;QAExG,mBAAmB;QACnB,MAAM,mBAAmB,CAAC,QAAQ,SAAS,IACzC,CAAC,QAAQ,SAAS,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI,SAAS,SAAS,CAAC,QAAQ,OAAO,QAAQ,SAAS;QAE5G,aAAa;QACb,MAAM,mBAAmB,QAAQ,WAAW,KAAK,QAAQ,SAAS,WAAW,KAAK,QAAQ,WAAW;QAErG,mBAAmB;QACnB,MAAM,mBAAmB,QAAQ,SAAS,CAAC,MAAM,KAAK,KACpD,QAAQ,SAAS,CAAC,KAAK,CAAC,CAAA,UAAW,SAAS,SAAS,CAAC,QAAQ,CAAC;QAEjE,OAAO,iBAAiB,mBAAmB,uBAAuB,sBAC3D,gBAAgB,mBAAmB,oBAAoB,oBAAoB;IACpF;IAEA,kBAAkB;IAClB,IAAI,WAAW,qBAAO,8OAAC;kBAAI;;;;;;IAE3B,MAAM,mBAAmB;WAAK,sBAAsB,EAAE;KAAE,CAAC,IAAI,CAAC,CAAC,GAAG;QAChE,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;YACL;gBACE,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAC1E;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;;;;;;;;0CAGtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAsB,CAAC,cAAc,EAAE,EAAE,MAAM;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;8CAEzB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAsB,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;;;;;;sDACvF,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;8CAEzB,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAsB,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,QAAQ,MAAM;;;;;;sDAClG,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC,oJAAA,CAAA,UAAe;gBACd,SAAS;gBACT,iBAAiB;gBACjB,gBAAgB;;;;;;0BAIlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAA+B,iBAAiB,MAAM;;;;;;4CAAQ;;;;;;;oCAE/E,QAAQ,UAAU,kBACjB,8OAAC;wCAAE,WAAU;;4CAAwB;4CAC7B,QAAQ,UAAU;4CAAC;;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;;;;;;;kDAI7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,2BAA2B,2CAA2C;0DAE9G,cAAA,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,2BAA2B,2CAA2C;0DAE9G,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;4BAAI,WAAW,aAAa,SACzB,6CACA;sCAED,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC,iJAAA,CAAA,UAAY;oCAEX,UAAU;oCACV,kBAAkB;oCAClB,YAAY,UAAU,QAAQ,CAAC,SAAS,EAAE;oCAC1C,eAAe,aAAa;oCAC5B,WAAW;mCALN,SAAS,EAAE;;;;;;;;;iDAUtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAOJ,iBAAiB,MAAM,GAAG,KAAK,iBAAiB,MAAM,IAAI,oBACzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAyI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzK", "debugId": null}}]}