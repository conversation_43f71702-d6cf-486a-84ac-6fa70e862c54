import React, { useState, useEffect } from 'react';
import { Building2, TrendingUp, Coins, FileText } from 'lucide-react';
import { useAccount } from 'wagmi';
import { useAuth } from '@/contexts/AuthContext';
import { useNFTOperations, useContractRead } from '@/hooks/useWeb3';

export default function HomeownerDashboard() {
  const { user } = useAuth();
  const { mintPropertyNFT } = useNFTOperations();
  const { address } = useAccount();
  const [tokenizedProperties, setTokenizedProperties] = useState([]);
  const [loading, setLoading] = useState(false);

  const { data: balance } = useContractRead('NFTi', 'balanceOf', address ? [address] : []);

  useEffect(() => {
    if (balance && address) {
      const fetchTokens = async () => {
        setLoading(true);
        const tokens = [];
        for (let i = 0; i < Number(balance); i++) {
          const tokenId = await useContractRead('NFTi', 'tokenOfOwnerByIndex', [address, i]);
          tokens.push({ id: tokenId, status: 'Active' });
        }
        setTokenizedProperties(tokens);
        setLoading(false);
      };
      fetchTokens();
    }
  }, [balance, address]);

  const handleTokenize = async () => {
    try {
      setLoading(true);
      const tokenId = Math.floor(Math.random() * 1000); // Dummy tokenId
      const tokenURI = 'ipfs://dummy-uri'; // Dummy URI
      await mintPropertyNFT(tokenId, tokenURI);
      alert('Property tokenized successfully!');
    } catch (error) {
      console.error(error);
      alert('Failed to tokenize property.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">Homeowner Dashboard</h2>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">My Properties</h3>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Performance</h3>
              <p className="text-2xl font-bold">N/A</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Coins className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Tokens</h3>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Reports</h3>
              <p className="text-2xl font-bold">0</p>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">My Tokenized Properties</h3>
        {loading ? (
          <p>Loading...</p>
        ) : tokenizedProperties.length > 0 ? (
          <div className="space-y-4">
            {tokenizedProperties.map((prop, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium">Property #{index + 1}</h4>
                <p className="text-sm text-gray-600">Token ID: {prop.id} | Status: {prop.status}</p>
              </div>
            ))}
          </div>
        ) : (
          <p>No tokenized properties yet.</p>
        )}
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="flex space-x-4">
          <button 
            onClick={handleTokenize}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Tokenizing...' : 'Tokenize Property'}
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
            List on Marketplace
          </button>
          <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
            Redeem Tokens
          </button>
        </div>
      </div>
    </div>
  );
}