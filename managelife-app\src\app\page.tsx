import Link from "next/link";
import { ArrowR<PERSON>, Building2, Coins, Shield, Users, Zap, TrendingUp } from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Building2 className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold gradient-text">ManageLife</span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/marketplace" className="text-gray-600 hover:text-blue-600 transition-colors">
                Marketplace
              </Link>
              <Link href="/about" className="text-gray-600 hover:text-blue-600 transition-colors">
                Team
              </Link>
              <a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-blue-600 transition-colors">
                Solutions
              </a>
              <Link href="/community" className="text-gray-600 hover:text-blue-600 transition-colors">
                Community
              </Link>
              <Link href="/docs" className="text-gray-600 hover:text-blue-600 transition-colors">
                Docs
              </Link>
              <a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-blue-600 transition-colors">
                Official Site
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/login"
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/auth/register"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Real-World Ownership,
              <span className="block gradient-text">Reinvented</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              MLife bridges institutional-grade real estate with the accessibility and programmability of blockchain. Own real assets, earn real returns, and govern your portfolio from anywhere.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://managelife.io/whitepaper"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:shadow-xl transition-all duration-300 web3-glow flex items-center justify-center"
              >
                Download Whitepaper
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
              <Link
                href="/auth/register"
                className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300"
              >
                Join the Waitlist
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Revolutionary Real Estate Platform
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the future of property investment with our comprehensive tokenization platform
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <Coins className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">NFT Tokenization</h3>
              <p className="text-gray-600">
                Convert your properties into NFTs (NFTi) and rental agreements into NFTr tokens for seamless blockchain-based management.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">$MLIFE Rewards</h3>
              <p className="text-gray-600">
                Earn $MLIFE tokens for timely rent payments, successful transactions, and community participation.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Secure & Transparent</h3>
              <p className="text-gray-600">
                All transactions are secured by blockchain technology with full transparency and immutable records.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-6">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Multi-Role Platform</h3>
              <p className="text-gray-600">
                Whether you're a homeowner, renter, buyer, or portfolio manager, we have tools designed for your needs.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-6">
                <Zap className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Instant Transactions</h3>
              <p className="text-gray-600">
                Fast and efficient property transactions with smart contracts automating the entire process.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                <Building2 className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Property Management</h3>
              <p className="text-gray-600">
                Comprehensive tools for managing properties, tenants, maintenance requests, and financial analytics.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Real Estate Portfolio?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of property owners, renters, and investors who are already using ManageLife to revolutionize their real estate experience.
          </p>
          <Link
            href="/auth/register"
            className="bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-all duration-300 inline-flex items-center"
          >
            Get Started Today
            <ArrowRight className="ml-2 w-5 h-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">ManageLife</span>
              </div>
              <p className="text-gray-400 mb-4">
                Revolutionizing real estate through blockchain technology and tokenization.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/marketplace" className="hover:text-white transition-colors">Marketplace</Link></li>
                <li><Link href="/dashboard" className="hover:text-white transition-colors">Dashboard</Link></li>
                <li><Link href="/tokenize" className="hover:text-white transition-colors">Tokenize Property</Link></li>
                <li><Link href="/rewards" className="hover:text-white transition-colors">Rewards</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Resources</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/docs" className="hover:text-white transition-colors">Documentation</Link></li>
                <li><a href="https://managelife.io/whitepaper" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Official Whitepaper</a></li>
                <li><Link href="/blog" className="hover:text-white transition-colors">Blog</Link></li>
                <li><a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Official Website</a></li>
                <li><Link href="/support" className="hover:text-white transition-colors">Support</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Community</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="https://www.facebook.com/ManageLife.IO" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Facebook</a></li>
                <li><a href="https://x.com/ManageLife_io" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Twitter/X</a></li>
                <li><a href="https://www.instagram.com/managelife.io/" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Instagram</a></li>
                <li><a href="https://www.linkedin.com/company/managelife-io/" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">LinkedIn</a></li>
                <li><a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">Official Website</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 ManageLife. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
