{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/layout/Navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Navigation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Navigation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/blog/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Navigation from '@/components/layout/Navigation';\nimport {\n  ArrowLeft,\n  Building2,\n  Calendar,\n  User,\n  Clock,\n  ArrowRight,\n  TrendingUp,\n  Coins,\n  Shield,\n  Zap\n} from 'lucide-react';\n\n// Mock blog posts data\nconst blogPosts = [\n  {\n    id: '1',\n    title: 'The Future of Real Estate: How Blockchain is Revolutionizing Property Investment',\n    excerpt: 'Discover how blockchain technology is transforming the real estate industry, making property investment more accessible and transparent than ever before.',\n    content: 'Full article content would go here...',\n    author: '<PERSON>',\n    authorRole: 'Head of Research',\n    publishedAt: '2024-01-15',\n    readTime: '8 min read',\n    category: 'Technology',\n    image: '/api/placeholder/600/300',\n    featured: true,\n  },\n  {\n    id: '2',\n    title: 'Understanding NFTi and NFTr: The Dual Token System Explained',\n    excerpt: 'Learn about our innovative dual token system that separates property ownership (NFTi) from rental rights (NFTr), creating new opportunities for investors.',\n    content: 'Full article content would go here...',\n    author: '<PERSON>',\n    authorRole: 'Blockchain Developer',\n    publishedAt: '2024-01-12',\n    readTime: '6 min read',\n    category: 'Education',\n    image: '/api/placeholder/600/300',\n    featured: false,\n  },\n  {\n    id: '3',\n    title: 'Maximizing Your $MLIFE Rewards: Tips and Strategies',\n    excerpt: 'Get the most out of the ManageLife platform with these proven strategies for earning and maximizing your $MLIFE token rewards.',\n    content: 'Full article content would go here...',\n    author: 'Emily Rodriguez',\n    authorRole: 'Community Manager',\n    publishedAt: '2024-01-10',\n    readTime: '5 min read',\n    category: 'Tips',\n    image: '/api/placeholder/600/300',\n    featured: false,\n  },\n  {\n    id: '4',\n    title: 'Market Analysis: Real Estate Tokenization Trends in 2024',\n    excerpt: 'An in-depth analysis of current market trends in real estate tokenization and what to expect in the coming year.',\n    content: 'Full article content would go here...',\n    author: 'David Park',\n    authorRole: 'Market Analyst',\n    publishedAt: '2024-01-08',\n    readTime: '10 min read',\n    category: 'Market Analysis',\n    image: '/api/placeholder/600/300',\n    featured: false,\n  },\n  {\n    id: '5',\n    title: 'Getting Started: Your First Property Tokenization',\n    excerpt: 'A step-by-step guide for property owners looking to tokenize their first property on the ManageLife platform.',\n    content: 'Full article content would go here...',\n    author: 'Lisa Wang',\n    authorRole: 'Product Manager',\n    publishedAt: '2024-01-05',\n    readTime: '7 min read',\n    category: 'Tutorial',\n    image: '/api/placeholder/600/300',\n    featured: false,\n  },\n  {\n    id: '6',\n    title: 'Community Spotlight: Success Stories from ManageLife Users',\n    excerpt: 'Read inspiring stories from our community members who have successfully leveraged the ManageLife platform for their real estate goals.',\n    content: 'Full article content would go here...',\n    author: 'Alex Thompson',\n    authorRole: 'Content Writer',\n    publishedAt: '2024-01-03',\n    readTime: '4 min read',\n    category: 'Community',\n    image: '/api/placeholder/600/300',\n    featured: false,\n  },\n];\n\nconst categories = ['All', 'Technology', 'Education', 'Tips', 'Market Analysis', 'Tutorial', 'Community'];\n\nexport default function BlogPage() {\n  const featuredPost = blogPosts.find(post => post.featured);\n  const regularPosts = blogPosts.filter(post => !post.featured);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n            ManageLife Blog\n          </h1>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Insights, tutorials, and updates from the world of real estate tokenization and blockchain technology.\n          </p>\n        </div>\n      </section>\n\n      {/* Featured Post */}\n      {featuredPost && (\n        <section className=\"py-16 bg-white\">\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-4xl mx-auto\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\n                <div className=\"md:flex\">\n                  <div className=\"md:w-1/2\">\n                    <img\n                      src={featuredPost.image}\n                      alt={featuredPost.title}\n                      className=\"w-full h-64 md:h-full object-cover\"\n                    />\n                  </div>\n                  <div className=\"md:w-1/2 p-8\">\n                    <div className=\"flex items-center space-x-2 mb-4\">\n                      <span className=\"bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded\">\n                        Featured\n                      </span>\n                      <span className=\"bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded\">\n                        {featuredPost.category}\n                      </span>\n                    </div>\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                      {featuredPost.title}\n                    </h2>\n                    <p className=\"text-gray-600 mb-6\">\n                      {featuredPost.excerpt}\n                    </p>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                        <div className=\"flex items-center\">\n                          <User className=\"w-4 h-4 mr-1\" />\n                          {featuredPost.author}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"w-4 h-4 mr-1\" />\n                          {new Date(featuredPost.publishedAt).toLocaleDateString()}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Clock className=\"w-4 h-4 mr-1\" />\n                          {featuredPost.readTime}\n                        </div>\n                      </div>\n                      <Link\n                        href={`/blog/${featuredPost.id}`}\n                        className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\"\n                      >\n                        Read More\n                        <ArrowRight className=\"w-4 h-4 ml-1\" />\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n      )}\n\n      {/* Categories Filter */}\n      <section className=\"py-8 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex flex-wrap justify-center gap-3\">\n            {categories.map((category) => (\n              <span\n                key={category}\n                className=\"px-4 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-colors cursor-pointer\"\n              >\n                {category}\n              </span>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts Grid */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {regularPosts.map((post) => (\n              <article key={post.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n                <img\n                  src={post.image}\n                  alt={post.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"p-6\">\n                  <div className=\"flex items-center space-x-2 mb-3\">\n                    <span className=\"bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded\">\n                      {post.category}\n                    </span>\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3 line-clamp-2\">\n                    {post.title}\n                  </h3>\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                    {post.excerpt}\n                  </p>\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <User className=\"w-4 h-4 mr-1\" />\n                      {post.author}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Clock className=\"w-4 h-4 mr-1\" />\n                      {post.readTime}\n                    </div>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">\n                      {new Date(post.publishedAt).toLocaleDateString()}\n                    </span>\n                    <Link\n                      href={`/blog/${post.id}`}\n                      className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\"\n                    >\n                      Read More\n                      <ArrowRight className=\"w-4 h-4 ml-1\" />\n                    </Link>\n                  </div>\n                </div>\n              </article>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter Signup */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"max-w-2xl mx-auto text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Stay Updated\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Subscribe to our newsletter for the latest insights on real estate tokenization and blockchain technology.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow cursor-pointer inline-block\">\n                Subscribe\n              </span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Related Topics */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Explore Topics</h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Dive deeper into the topics that matter most in real estate tokenization\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <TrendingUp className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Market Trends</h3>\n              <p className=\"text-gray-600 text-sm\">Latest trends and analysis in real estate tokenization</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Coins className=\"w-8 h-8 text-green-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Token Economy</h3>\n              <p className=\"text-gray-600 text-sm\">Understanding $MLIFE and the platform economy</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Shield className=\"w-8 h-8 text-purple-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Security</h3>\n              <p className=\"text-gray-600 text-sm\">Blockchain security and best practices</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <Zap className=\"w-8 h-8 text-orange-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Innovation</h3>\n              <p className=\"text-gray-600 text-sm\">Latest innovations in PropTech and blockchain</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAaA,uBAAuB;AACvB,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAO;IAAc;IAAa;IAAQ;IAAmB;IAAY;CAAY;AAE1F,SAAS;IACtB,MAAM,eAAe,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ;IACzD,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;;;;;;YAO/D,8BACC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,aAAa,KAAK;4CACvB,KAAK,aAAa,KAAK;4CACvB,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAsE;;;;;;kEAGtF,8OAAC;wDAAK,WAAU;kEACb,aAAa,QAAQ;;;;;;;;;;;;0DAG1B,8OAAC;gDAAG,WAAU;0DACX,aAAa,KAAK;;;;;;0DAErB,8OAAC;gDAAE,WAAU;0DACV,aAAa,OAAO;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf,aAAa,MAAM;;;;;;;0EAEtB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,IAAI,KAAK,aAAa,WAAW,EAAE,kBAAkB;;;;;;;0EAExD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,aAAa,QAAQ;;;;;;;;;;;;;kEAG1B,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,EAAE;wDAChC,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYxC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;;;;;;0BAWf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gCAAsB,WAAU;;kDAC/B,8OAAC;wCACC,KAAK,KAAK,KAAK;wCACf,KAAK,KAAK,KAAK;wCACf,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;0DAGlB,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,KAAK,MAAM;;;;;;;kEAEd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,KAAK,QAAQ;;;;;;;;;;;;;0DAGlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;kEAEhD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;wDACxB,WAAU;;4DACX;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;+BArChB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;0BAgD7B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAA+K;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvM,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}