module.exports = {

"[project]/.next-internal/server/app/api/community/events/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "db": ()=>db,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
;
;
;
const DB_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const USERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'users.json');
const JWT_SECRET = process.env.JWT_SECRET || 'managelife-secret-key-2025';
// Ensure data directory exists
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DB_PATH)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DB_PATH, {
        recursive: true
    });
}
// Initialize users file if it doesn't exist
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USERS_FILE)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify([], null, 2));
}
class Database {
    readUsers() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(USERS_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error('Error reading users file:', error);
            return [];
        }
    }
    writeUsers(users) {
        try {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
        } catch (error) {
            console.error('Error writing users file:', error);
            throw new Error('Failed to save user data');
        }
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    generateToken(userId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign({
            userId
        }, JWT_SECRET, {
            expiresIn: '7d'
        });
    }
    async createUser(userData) {
        const users = this.readUsers();
        // Check if user already exists
        const existingUser = users.find((u)=>userData.email && u.email === userData.email || userData.walletAddress && u.walletAddress === userData.walletAddress);
        if (existingUser) {
            throw new Error('User already exists');
        }
        // Hash password if provided
        let hashedPassword;
        if (userData.password) {
            hashedPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(userData.password, 12);
        }
        const newUser = {
            id: this.generateId(),
            email: userData.email,
            name: userData.name,
            password: hashedPassword,
            walletAddress: userData.walletAddress,
            roles: userData.roles,
            kycStatus: 'pending',
            joinedAt: new Date().toISOString(),
            mlifeBalance: 1000,
            isEmailVerified: false,
            lastLogin: new Date().toISOString()
        };
        users.push(newUser);
        this.writeUsers(users);
        const token = this.generateToken(newUser.id);
        // Remove password from response
        const { password, ...userResponse } = newUser;
        return {
            user: userResponse,
            token
        };
    }
    async authenticateUser(email, password) {
        const users = this.readUsers();
        const user = users.find((u)=>u.email === email);
        if (!user || !user.password) {
            throw new Error('Invalid credentials');
        }
        const isValidPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, user.password);
        if (!isValidPassword) {
            throw new Error('Invalid credentials');
        }
        // Update last login
        user.lastLogin = new Date().toISOString();
        this.writeUsers(users);
        const token = this.generateToken(user.id);
        // Remove password from response
        const { password: _, ...userResponse } = user;
        return {
            user: userResponse,
            token
        };
    }
    async authenticateWallet(walletAddress) {
        const users = this.readUsers();
        let user = users.find((u)=>u.walletAddress === walletAddress.toLowerCase());
        let isNewUser = false;
        if (!user) {
            // Create new user with wallet
            const newUserData = await this.createUser({
                walletAddress: walletAddress.toLowerCase(),
                name: `User ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`,
                roles: [
                    'buyer'
                ]
            });
            user = newUserData.user;
            isNewUser = true;
        } else {
            // Update last login
            user.lastLogin = new Date().toISOString();
            this.writeUsers(users);
        }
        const token = this.generateToken(user.id);
        // Remove password from response
        const { password, ...userResponse } = user;
        return {
            user: userResponse,
            token,
            isNewUser
        };
    }
    async getUserById(userId) {
        const users = this.readUsers();
        const user = users.find((u)=>u.id === userId);
        if (!user) {
            return null;
        }
        // Remove password from response
        const { password, ...userResponse } = user;
        return userResponse;
    }
    async updateUser(userId, updates) {
        const users = this.readUsers();
        const userIndex = users.findIndex((u)=>u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        // Don't allow updating sensitive fields directly
        const { id, password, ...allowedUpdates } = updates;
        users[userIndex] = {
            ...users[userIndex],
            ...allowedUpdates
        };
        this.writeUsers(users);
        // Remove password from response
        const { password: _, ...userResponse } = users[userIndex];
        return userResponse;
    }
    // Update user's MLIFE balance
    async updateUserBalance(userId, amount) {
        const users = this.readUsers();
        const userIndex = users.findIndex((u)=>u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        users[userIndex].mlifeBalance += amount;
        this.writeUsers(users);
    }
    async linkWalletToUser(userId, walletAddress) {
        const users = this.readUsers();
        const userIndex = users.findIndex((u)=>u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        // Check if wallet is already linked to another user
        const existingWalletUser = users.find((u)=>u.walletAddress === walletAddress.toLowerCase() && u.id !== userId);
        if (existingWalletUser) {
            throw new Error('Wallet already linked to another account');
        }
        users[userIndex].walletAddress = walletAddress.toLowerCase();
        this.writeUsers(users);
        // Remove password from response
        const { password, ...userResponse } = users[userIndex];
        return userResponse;
    }
    verifyToken(token) {
        try {
            const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
            return decoded;
        } catch (error) {
            return null;
        }
    }
    async getAllUsers() {
        const users = this.readUsers();
        // Remove passwords from response
        return users.map(({ password, ...user })=>user);
    }
    async updateUserBalance(userId, amount) {
        const users = this.readUsers();
        const userIndex = users.findIndex((u)=>u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        users[userIndex].mlifeBalance += amount;
        this.writeUsers(users);
        // Remove password from response
        const { password, ...userResponse } = users[userIndex];
        return userResponse;
    }
}
const db = new Database();
const __TURBOPACK__default__export__ = db;
}),
"[project]/src/lib/community.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "communityService": ()=>communityService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DB_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const EVENTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'community-events.json');
const PARTICIPANTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'event-participants.json');
const POSTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'community-posts.json');
const COMMENTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'post-comments.json');
const LIKES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'post-likes.json');
const PROFILES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'user-profiles.json');
// Ensure data directory exists
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DB_PATH)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DB_PATH, {
        recursive: true
    });
}
// Initialize files if they don't exist
const initializeFile = (filePath, defaultData)=>{
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(defaultData, null, 2));
    }
};
// Initialize with sample data
const sampleEvents = [
    {
        id: 'event-1',
        title: 'Real Estate Investment Fundamentals',
        description: 'Learn the basics of real estate investment, including market analysis, financing options, and risk management strategies.',
        type: 'webinar',
        startDate: new Date('2025-02-15T19:00:00Z'),
        endDate: new Date('2025-02-15T20:30:00Z'),
        isVirtual: true,
        maxParticipants: 100,
        currentParticipants: 45,
        organizerId: 'admin-1',
        status: 'upcoming',
        tags: [
            'investment',
            'beginner',
            'fundamentals'
        ],
        rewardAmount: 25,
        createdAt: new Date('2025-01-15T10:00:00Z'),
        updatedAt: new Date('2025-01-15T10:00:00Z')
    },
    {
        id: 'event-2',
        title: 'Property Management Best Practices',
        description: 'Workshop covering tenant screening, maintenance scheduling, and legal compliance for property managers.',
        type: 'workshop',
        startDate: new Date('2025-02-20T14:00:00Z'),
        endDate: new Date('2025-02-20T17:00:00Z'),
        location: 'Downtown Conference Center',
        isVirtual: false,
        maxParticipants: 30,
        currentParticipants: 18,
        organizerId: 'admin-1',
        status: 'upcoming',
        tags: [
            'property-management',
            'workshop',
            'legal'
        ],
        rewardAmount: 50,
        createdAt: new Date('2025-01-20T10:00:00Z'),
        updatedAt: new Date('2025-01-20T10:00:00Z')
    }
];
const samplePosts = [
    {
        id: 'post-1',
        authorId: 'user-1',
        title: 'Market Trends: What to Expect in 2025',
        content: 'Based on recent data and expert analysis, here are the key real estate market trends we should watch for in 2025...',
        type: 'discussion',
        category: 'market_trends',
        tags: [
            '2025',
            'trends',
            'analysis'
        ],
        likes: 24,
        comments: 8,
        shares: 5,
        isSticky: true,
        status: 'published',
        createdAt: new Date('2025-01-10T09:00:00Z'),
        updatedAt: new Date('2025-01-10T09:00:00Z')
    },
    {
        id: 'post-2',
        authorId: 'user-2',
        title: 'First-time Buyer Tips',
        content: 'As someone who just bought their first property, here are some tips I wish I knew earlier...',
        type: 'tip',
        category: 'general',
        tags: [
            'first-time',
            'tips',
            'buying'
        ],
        likes: 18,
        comments: 12,
        shares: 7,
        isSticky: false,
        status: 'published',
        createdAt: new Date('2025-01-12T14:30:00Z'),
        updatedAt: new Date('2025-01-12T14:30:00Z')
    }
];
initializeFile(EVENTS_FILE, sampleEvents);
initializeFile(PARTICIPANTS_FILE, []);
initializeFile(POSTS_FILE, samplePosts);
initializeFile(COMMENTS_FILE, []);
initializeFile(LIKES_FILE, []);
initializeFile(PROFILES_FILE, []);
class CommunityService {
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Event management
    async getEvents(filters) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(EVENTS_FILE, 'utf8');
            let events = JSON.parse(data);
            // Convert date strings back to Date objects
            events = events.map((event)=>({
                    ...event,
                    startDate: new Date(event.startDate),
                    endDate: new Date(event.endDate),
                    createdAt: new Date(event.createdAt),
                    updatedAt: new Date(event.updatedAt)
                }));
            // Apply filters
            if (filters) {
                if (filters.type) {
                    events = events.filter((event)=>event.type === filters.type);
                }
                if (filters.status) {
                    events = events.filter((event)=>event.status === filters.status);
                }
                if (filters.upcoming) {
                    events = events.filter((event)=>new Date(event.startDate) > new Date());
                }
            }
            // Sort by start date
            return events.sort((a, b)=>new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
        } catch (error) {
            console.error('Error reading events:', error);
            return [];
        }
    }
    async getEventById(eventId) {
        const events = await this.getEvents();
        return events.find((event)=>event.id === eventId) || null;
    }
    async createEvent(eventData) {
        const events = await this.getEvents();
        const newEvent = {
            ...eventData,
            id: this.generateId(),
            currentParticipants: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        events.push(newEvent);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(EVENTS_FILE, JSON.stringify(events, null, 2));
        return newEvent;
    }
    // Event participation
    async registerForEvent(eventId, userId) {
        const participants = this.readParticipants();
        // Check if already registered
        const existing = participants.find((p)=>p.eventId === eventId && p.userId === userId);
        if (existing) {
            throw new Error('Already registered for this event');
        }
        const newParticipant = {
            id: this.generateId(),
            eventId,
            userId,
            status: 'registered',
            registeredAt: new Date()
        };
        participants.push(newParticipant);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(PARTICIPANTS_FILE, JSON.stringify(participants, null, 2));
        // Update event participant count
        await this.updateEventParticipantCount(eventId);
        return newParticipant;
    }
    readParticipants() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(PARTICIPANTS_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            return [];
        }
    }
    async updateEventParticipantCount(eventId) {
        const events = await this.getEvents();
        const participants = this.readParticipants();
        const eventIndex = events.findIndex((e)=>e.id === eventId);
        if (eventIndex !== -1) {
            const participantCount = participants.filter((p)=>p.eventId === eventId && p.status !== 'cancelled').length;
            events[eventIndex].currentParticipants = participantCount;
            events[eventIndex].updatedAt = new Date();
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(EVENTS_FILE, JSON.stringify(events, null, 2));
        }
    }
    // Posts management
    async getPosts(filters) {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(POSTS_FILE, 'utf8');
            let posts = JSON.parse(data);
            // Convert date strings back to Date objects
            posts = posts.map((post)=>({
                    ...post,
                    createdAt: new Date(post.createdAt),
                    updatedAt: new Date(post.updatedAt)
                }));
            // Apply filters
            if (filters) {
                if (filters.category) {
                    posts = posts.filter((post)=>post.category === filters.category);
                }
                if (filters.type) {
                    posts = posts.filter((post)=>post.type === filters.type);
                }
                if (filters.authorId) {
                    posts = posts.filter((post)=>post.authorId === filters.authorId);
                }
            }
            // Sort by creation date (newest first), with sticky posts at top
            return posts.sort((a, b)=>{
                if (a.isSticky && !b.isSticky) return -1;
                if (!a.isSticky && b.isSticky) return 1;
                return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
            });
        } catch (error) {
            console.error('Error reading posts:', error);
            return [];
        }
    }
    async createPost(postData) {
        const posts = await this.getPosts();
        const newPost = {
            ...postData,
            id: this.generateId(),
            likes: 0,
            comments: 0,
            shares: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        posts.push(newPost);
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(POSTS_FILE, JSON.stringify(posts, null, 2));
        return newPost;
    }
    // Community stats
    async getCommunityStats() {
        const events = await this.getEvents();
        const posts = await this.getPosts();
        const participants = this.readParticipants();
        return {
            totalEvents: events.length,
            upcomingEvents: events.filter((e)=>e.status === 'upcoming').length,
            totalPosts: posts.length,
            totalParticipants: participants.length,
            activeMembers: new Set(participants.map((p)=>p.userId)).size
        };
    }
}
const communityService = new CommunityService();
const __TURBOPACK__default__export__ = communityService;
}),
"[project]/src/lib/rewards.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "rewardSystem": ()=>rewardSystem
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DB_PATH = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const REWARDS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'rewards.json');
const REWARD_RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'reward-rules.json');
const USER_STATS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DB_PATH, 'user-reward-stats.json');
// Ensure data directory exists
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DB_PATH)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DB_PATH, {
        recursive: true
    });
}
// Initialize files if they don't exist
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(REWARDS_FILE)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(REWARDS_FILE, JSON.stringify([], null, 2));
}
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(REWARD_RULES_FILE)) {
    // Initialize with default reward rules
    const defaultRules = [
        {
            id: 'welcome_bonus',
            type: 'welcome_bonus',
            name: 'Welcome Bonus',
            description: 'Welcome to ManageLife! Here\'s your starter bonus.',
            amount: 1000,
            conditions: {},
            isActive: true,
            maxClaims: 1
        },
        {
            id: 'daily_login',
            type: 'daily_login',
            name: 'Daily Login Bonus',
            description: 'Login daily to earn rewards!',
            amount: 10,
            conditions: {},
            isActive: true,
            cooldownPeriod: 24
        },
        {
            id: 'rent_payment',
            type: 'rent_payment',
            name: 'On-time Rent Payment',
            description: 'Reward for paying rent on time',
            amount: 50,
            conditions: {
                onTime: true
            },
            isActive: true
        },
        {
            id: 'property_listing',
            type: 'property_listing',
            name: 'Property Listing Bonus',
            description: 'Reward for listing a property',
            amount: 100,
            conditions: {},
            isActive: true
        },
        {
            id: 'referral',
            type: 'referral',
            name: 'Referral Bonus',
            description: 'Reward for referring new users',
            amount: 200,
            conditions: {},
            isActive: true
        },
        {
            id: 'kyc_completion',
            type: 'kyc_completion',
            name: 'KYC Completion Bonus',
            description: 'Reward for completing KYC verification',
            amount: 150,
            conditions: {},
            isActive: true,
            maxClaims: 1
        },
        {
            id: 'community_participation',
            type: 'community_participation',
            name: 'Community Participation',
            description: 'Reward for participating in community activities',
            amount: 25,
            conditions: {},
            isActive: true,
            cooldownPeriod: 1
        }
    ];
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(REWARD_RULES_FILE, JSON.stringify(defaultRules, null, 2));
}
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(USER_STATS_FILE)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USER_STATS_FILE, JSON.stringify([], null, 2));
}
class RewardSystem {
    readRewards() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(REWARDS_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error('Error reading rewards file:', error);
            return [];
        }
    }
    writeRewards(rewards) {
        try {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(REWARDS_FILE, JSON.stringify(rewards, null, 2));
        } catch (error) {
            console.error('Error writing rewards file:', error);
            throw new Error('Failed to save reward data');
        }
    }
    readRewardRules() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(REWARD_RULES_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error('Error reading reward rules file:', error);
            return [];
        }
    }
    readUserStats() {
        try {
            const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(USER_STATS_FILE, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error('Error reading user stats file:', error);
            return [];
        }
    }
    writeUserStats(stats) {
        try {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(USER_STATS_FILE, JSON.stringify(stats, null, 2));
        } catch (error) {
            console.error('Error writing user stats file:', error);
            throw new Error('Failed to save user stats');
        }
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Create a new reward
    async createReward(userId, type, amount, description, metadata) {
        const rewards = this.readRewards();
        const newReward = {
            id: this.generateId(),
            userId,
            amount,
            source: type,
            description,
            status: 'pending',
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            metadata
        };
        rewards.push(newReward);
        this.writeRewards(rewards);
        // Update user stats
        await this.updateUserStats(userId, amount, 0);
        return newReward;
    }
    // Get rewards for a user
    async getUserRewards(userId) {
        const rewards = this.readRewards();
        return rewards.filter((reward)=>reward.userId === userId);
    }
    // Get pending rewards for a user
    async getPendingRewards(userId) {
        const rewards = await this.getUserRewards(userId);
        return rewards.filter((reward)=>reward.status === 'pending' && (!reward.expiresAt || new Date(reward.expiresAt) > new Date()));
    }
    // Claim a reward
    async claimReward(rewardId, userId) {
        const rewards = this.readRewards();
        const rewardIndex = rewards.findIndex((r)=>r.id === rewardId && r.userId === userId);
        if (rewardIndex === -1) {
            throw new Error('Reward not found');
        }
        const reward = rewards[rewardIndex];
        if (reward.status !== 'pending') {
            throw new Error('Reward already claimed or expired');
        }
        if (reward.expiresAt && new Date(reward.expiresAt) <= new Date()) {
            reward.status = 'expired';
            this.writeRewards(rewards);
            throw new Error('Reward has expired');
        }
        reward.status = 'claimed';
        reward.claimedAt = new Date();
        this.writeRewards(rewards);
        // Update user stats
        await this.updateUserStats(userId, 0, reward.amount);
        return reward;
    }
    // Claim all pending rewards for a user
    async claimAllRewards(userId) {
        const pendingRewards = await this.getPendingRewards(userId);
        const claimed = [];
        let total = 0;
        for (const reward of pendingRewards){
            try {
                const claimedReward = await this.claimReward(reward.id, userId);
                claimed.push(claimedReward);
                total += claimedReward.amount;
            } catch (error) {
                console.error(`Failed to claim reward ${reward.id}:`, error);
            }
        }
        return {
            claimed,
            total
        };
    }
    // Award reward based on activity
    async awardReward(userId, type, metadata) {
        const rules = this.readRewardRules();
        const rule = rules.find((r)=>r.type === type && r.isActive);
        if (!rule) {
            console.log(`No active rule found for reward type: ${type}`);
            return null;
        }
        // Check cooldown period
        if (rule.cooldownPeriod) {
            const userRewards = await this.getUserRewards(userId);
            const lastReward = userRewards.filter((r)=>r.source === type).sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
            if (lastReward) {
                const timeSinceLastReward = Date.now() - new Date(lastReward.createdAt).getTime();
                const cooldownMs = rule.cooldownPeriod * 60 * 60 * 1000;
                if (timeSinceLastReward < cooldownMs) {
                    console.log(`Cooldown period not met for reward type: ${type}`);
                    return null;
                }
            }
        }
        // Check max claims
        if (rule.maxClaims) {
            const userRewards = await this.getUserRewards(userId);
            const claimsCount = userRewards.filter((r)=>r.source === type).length;
            if (claimsCount >= rule.maxClaims) {
                console.log(`Max claims reached for reward type: ${type}`);
                return null;
            }
        }
        return await this.createReward(userId, type, rule.amount, rule.description, metadata);
    }
    // Get user reward statistics
    async getUserStats(userId) {
        const allStats = this.readUserStats();
        let userStats = allStats.find((s)=>s.userId === userId);
        if (!userStats) {
            userStats = {
                userId,
                totalEarned: 0,
                totalClaimed: 0,
                pendingRewards: 0,
                streakDays: 0,
                achievements: []
            };
            allStats.push(userStats);
            this.writeUserStats(allStats);
        }
        // Update pending rewards count
        const pendingRewards = await this.getPendingRewards(userId);
        userStats.pendingRewards = pendingRewards.reduce((sum, reward)=>sum + reward.amount, 0);
        return userStats;
    }
    // Update user statistics
    async updateUserStats(userId, earnedAmount, claimedAmount) {
        const allStats = this.readUserStats();
        let userStatsIndex = allStats.findIndex((s)=>s.userId === userId);
        if (userStatsIndex === -1) {
            allStats.push({
                userId,
                totalEarned: earnedAmount,
                totalClaimed: claimedAmount,
                pendingRewards: 0,
                streakDays: 0,
                achievements: []
            });
        } else {
            allStats[userStatsIndex].totalEarned += earnedAmount;
            allStats[userStatsIndex].totalClaimed += claimedAmount;
            if (claimedAmount > 0) {
                allStats[userStatsIndex].lastClaimDate = new Date();
            }
        }
        this.writeUserStats(allStats);
    }
    // Get reward rules
    async getRewardRules() {
        return this.readRewardRules();
    }
    // Get leaderboard
    async getLeaderboard(limit = 10) {
        const allStats = this.readUserStats();
        return allStats.sort((a, b)=>b.totalClaimed - a.totalClaimed).slice(0, limit);
    }
}
const rewardSystem = new RewardSystem();
const __TURBOPACK__default__export__ = rewardSystem;
}),
"[project]/src/app/api/community/events/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$community$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/community.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rewards$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/rewards.ts [app-route] (ecmascript)");
;
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const type = searchParams.get('type');
        const status = searchParams.get('status');
        const upcoming = searchParams.get('upcoming') === 'true';
        const filters = {};
        if (type) filters.type = type;
        if (status) filters.status = status;
        if (upcoming) filters.upcoming = upcoming;
        const events = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$community$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["communityService"].getEvents(filters);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            events
        });
    } catch (error) {
        console.error('Get events error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const token = request.cookies.get('auth-token')?.value;
        if (!token) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No authentication token found'
            }, {
                status: 401
            });
        }
        // Verify token
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].verifyToken(token);
        if (!decoded) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid or expired token'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { action, eventId } = body;
        if (action === 'register') {
            if (!eventId) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Event ID is required'
                }, {
                    status: 400
                });
            }
            // Check if event exists
            const event = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$community$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["communityService"].getEventById(eventId);
            if (!event) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Event not found'
                }, {
                    status: 404
                });
            }
            // Check if event is full
            if (event.maxParticipants && event.currentParticipants >= event.maxParticipants) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Event is full'
                }, {
                    status: 400
                });
            }
            // Register for event
            const participant = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$community$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["communityService"].registerForEvent(eventId, decoded.userId);
            // Award community participation reward
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$rewards$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rewardSystem"].awardReward(decoded.userId, 'community_participation', {
                    eventId,
                    eventTitle: event.title
                });
            } catch (rewardError) {
                console.error('Failed to award community participation reward:', rewardError);
            // Don't fail registration if reward fails
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Successfully registered for event',
                participant
            });
        } else if (action === 'create') {
            // Only allow certain roles to create events (for now, just check if user exists)
            const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].getUserById(decoded.userId);
            if (!user) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'User not found'
                }, {
                    status: 404
                });
            }
            const { title, description, type, startDate, endDate, location, isVirtual, maxParticipants, tags, rewardAmount } = body;
            if (!title || !description || !type || !startDate || !endDate) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Missing required fields'
                }, {
                    status: 400
                });
            }
            const eventData = {
                title,
                description,
                type,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                location,
                isVirtual: isVirtual || false,
                maxParticipants,
                organizerId: decoded.userId,
                status: 'upcoming',
                tags: tags || [],
                rewardAmount
            };
            const event = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$community$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["communityService"].createEvent(eventData);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Event created successfully',
                event
            });
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid action'
            }, {
                status: 400
            });
        }
    } catch (error) {
        console.error('Event action error:', error);
        if (error.message.includes('Already registered')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: error.message
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5c03d8a8._.js.map